<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright (c) 2016, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    <!-- Picture-in-Picture (PIP) notification -->
    <!-- Title for the notification channel for TV PIP controls. [CHAR LIMIT=NONE] -->
    <string name="notification_channel_tv_pip">Picture-in-Picture</string>
    <!-- Title of the picture-in-picture (PIP) notification title
         when the media doesn't have title [CHAR LIMIT=NONE] -->
    <string name="pip_notification_unknown_title">(No title program)</string>

    <!-- Picture-in-Picture (PIP) menu -->
    <eat-comment />
    <!-- Button to close picture-in-picture (PIP) in PIP menu [CHAR LIMIT=30] -->
    <string name="pip_close">Close PIP</string>
    <!-- Button to move picture-in-picture (PIP) screen to the fullscreen in PIP menu [CHAR LIMIT=30] -->
    <string name="pip_fullscreen">Full screen</string>
</resources>
