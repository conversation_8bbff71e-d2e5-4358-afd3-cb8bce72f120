<?xml version="1.0" encoding="utf-8"?><!-- Copyright (C) 2006 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <style name="RecentsTheme" parent="@android:style/Theme.Material">
        <!-- NoTitle -->
        <item name="android:windowNoTitle">true</item>
        <!-- Misc -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:ambientShadowAlpha">0.35</item>
    </style>

    <!-- Recents theme -->
    <style name="RecentsTheme.Wallpaper">
        <item name="android:windowBackground">@*android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowShowWallpaper">true</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="clearAllStyle">@style/ClearAllButtonDefaultMargins</item>
        <item name="clearAllBackgroundColor">@color/recents_clear_all_button_bg_dark_color</item>
        <item name="wallpaperTextColor">@*android:color/primary_text_material_dark</item>
        <item name="wallpaperTextColorSecondary">@*android:color/secondary_text_material_dark</item>
    </style>

    <style name="RecentsTheme.Wallpaper.Light">
        <item name="clearAllBackgroundColor">@color/recents_clear_all_button_bg_light_color</item>
        <item name="wallpaperTextColor">@*android:color/primary_text_material_light</item>
        <item name="wallpaperTextColorSecondary">@*android:color/secondary_text_material_light
        </item>
    </style>

    <style name="ClearAllButtonDefaultMargins">
        <item name="android:layout_marginStart">0dp</item>
        <item name="android:layout_marginTop">0dp</item>
        <item name="android:layout_marginEnd">0dp</item>
        <item name="android:layout_marginBottom">0dp</item>
    </style>

    <!-- Performance optimized Recents theme (no wallpaper) -->
    <style name="RecentsTheme.NoWallpaper">
        <item name="android:windowBackground">@android:color/black</item>
        <item name="wallpaperTextColor">@android:color/white</item>
        <item name="wallpaperTextColorSecondary">@android:color/white</item>
    </style>

    <!-- Theme used for the activity that shows when the system forced an app to be resizable -->
    <style name="ForcedResizableTheme" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:windowBackground">@drawable/forced_resizable_background</item>
        <item name="android:statusBarColor">@*android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.ForcedResizable</item>
    </style>

    <style name="Animation.ForcedResizable" parent="@android:style/Animation">
        <item name="android:activityOpenEnterAnimation">@anim/forced_resizable_enter</item>

        <!-- If the target stack doesn't have focus, we do a task to front animation. -->
        <item name="android:taskToFrontEnterAnimation">@anim/forced_resizable_enter</item>
        <item name="android:activityCloseExitAnimation">@anim/forced_resizable_exit</item>
    </style>

    <style name="PipPhoneOverlayControlTheme" parent="@android:style/Theme.Material">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:statusBarColor">@*android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.PipPhoneOverlayControl</item>
    </style>

    <style name="Animation.PipPhoneOverlayControl" parent="@android:style/Animation">
        <item name="android:activityOpenEnterAnimation">@anim/forced_resizable_enter</item>

        <!-- If the target stack doesn't have focus, we do a task to front animation. -->
        <item name="android:taskToFrontEnterAnimation">@anim/forced_resizable_enter</item>
        <item name="android:activityCloseExitAnimation">@anim/forced_resizable_exit</item>
    </style>

    <!-- HybridNotification themes and styles -->

    <style name="HybridNotification">
        <item name="hybridNotificationStyle">@style/hybrid_notification</item>
        <item name="hybridNotificationTitleStyle">@style/hybrid_notification_title</item>
        <item name="hybridNotificationTextStyle">@style/hybrid_notification_text</item>
    </style>

    <style name="HybridNotification.Ambient">
        <item name="hybridNotificationStyle">@style/hybrid_notification_ambient</item>
        <item name="hybridNotificationTitleStyle">@style/hybrid_notification_title_ambient</item>
        <item name="hybridNotificationTextStyle">@style/hybrid_notification_text_ambient</item>
    </style>

    <style name="hybrid_notification_ambient">
        <item name="android:paddingStart">@*android:dimen/notification_extra_margin_ambient</item>
        <item name="android:paddingEnd">@*android:dimen/notification_extra_margin_ambient</item>
        <item name="android:orientation">vertical</item>
    </style>

    <style name="hybrid_notification">
        <item name="android:paddingStart">@*android:dimen/notification_content_margin_start</item>
        <item name="android:paddingEnd">12dp</item>
    </style>

    <style name="hybrid_notification_title_ambient">
        <item name="android:layout_marginTop">
            @*android:dimen/notification_header_margin_top_ambient
        </item>
        <item name="android:paddingStart">@*android:dimen/notification_content_margin_start</item>
        <item name="android:paddingEnd">@*android:dimen/notification_content_margin_end</item>
        <item name="android:textAppearance">@*android:style/Notification.Header.Ambient</item>
        <item name="android:layout_gravity">top|center_horizontal</item>
        <item name="android:textSize">@*android:dimen/notification_ambient_title_text_size</item>
        <item name="android:textColor">#ffffffff</item>
    </style>

    <style name="hybrid_notification_title">
        <item name="android:paddingEnd">4dp</item>
        <item name="android:textAppearance">
            @*android:style/TextAppearance.Material.Notification.Title
        </item>
    </style>

    <style name="hybrid_notification_text_ambient">
        <item name="android:paddingStart">@*android:dimen/notification_content_margin_start</item>
        <item name="android:paddingEnd">@*android:dimen/notification_content_margin_end</item>
        <item name="android:textSize">@*android:dimen/notification_ambient_text_size</item>
        <item name="android:textColor">#eeffffff</item>
        <item name="android:gravity">top|center_horizontal</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">3</item>
    </style>

    <style name="hybrid_notification_text" parent="@*android:style/Widget.Material.Notification.Text">
        <item name="android:paddingEnd">4dp</item>
    </style>


    <style name="TextAppearance.StatusBar.HeadsUp" parent="@*android:style/TextAppearance.StatusBar"></style>

    <style name="TextAppearance.StatusBar.SystemPanel" parent="@*android:style/TextAppearance.StatusBar">
        <item name="android:textAppearance">?android:attr/textAppearance</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">#FF808080</item>
    </style>

    <style name="TextAppearance.StatusBar.Clock" parent="@*android:style/TextAppearance.StatusBar.Icon">
        <item name="android:textSize">@dimen/status_bar_clock_size</item>
        <item name="android:fontFamily">@font/mitype2018_80</item>
        <item name="android:textColor">@color/status_bar_clock_color</item>
    </style>

    <style name="TextAppearance.StatusBar.Expanded" parent="@*android:style/TextAppearance.StatusBar">
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>

    <style name="TextAppearance.StatusBar.Expanded.Clock">
        <item name="android:textSize">@dimen/qs_time_expanded_size</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="TextAppearance.StatusBar.Expanded.Date">
        <item name="android:textSize">@dimen/qs_time_expanded_size</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.StatusBar.Expanded.AboveDateTime">
        <item name="android:textSize">@dimen/qs_emergency_calls_only_text_size</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>

    <style name="TextAppearance.StatusBar.Expanded.EmergencyCallsOnly" parent="TextAppearance.StatusBar.Expanded.AboveDateTime" />

    <style name="TextAppearance.StatusBar.Expanded.ChargingInfo" parent="TextAppearance.StatusBar.Expanded.AboveDateTime" />

    <style name="TextAppearance.StatusBar.Expanded.UserSwitcher">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="TextAppearance.StatusBar.Expanded.UserSwitcher.UserName" />

    <style name="TextAppearance" />

    <style name="TextAppearance.QS">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.QS.DetailHeader">
        <item name="android:textSize">@dimen/qs_detail_header_text_size</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.QS.DetailItemPrimary">
        <item name="android:textSize">@dimen/qs_detail_item_primary_text_size</item>
    </style>

    <style name="TextAppearance.QS.DetailItemSecondary">
        <item name="android:textSize">@dimen/qs_detail_item_secondary_text_size</item>
        <item name="android:textColor">?android:attr/colorAccent</item>
    </style>

    <style name="TextAppearance.QS.Introduction">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/zen_introduction</item>
    </style>

    <style name="TextAppearance.QS.Warning">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:attr/colorError</item>
    </style>

    <style name="TextAppearance.QS.DetailButton">
        <item name="android:textSize">@dimen/qs_detail_button_text_size</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="TextAppearance.QS.DetailButton.White">
        <item name="android:textColor">@color/zen_introduction</item>
    </style>

    <style name="TextAppearance.QS.DetailEmpty">
        <item name="android:textSize">@dimen/qs_detail_empty_text_size</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="TextAppearance.QS.Subhead">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/qs_subhead</item>
    </style>

    <style name="TextAppearance.QS.SegmentedButton">
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.QS.DataUsage">
        <item name="android:textSize">@dimen/qs_data_usage_text_size</item>
    </style>

    <style name="TextAppearance.QS.DataUsage.Usage">
        <item name="android:textSize">@dimen/qs_data_usage_usage_text_size</item>
        <item name="android:textColor">?android:attr/colorAccent</item>
    </style>

    <style name="TextAppearance.QS.DataUsage.Secondary">
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="TextAppearance.QS.TileLabel">
        <item name="android:textSize">@dimen/qs_tile_text_size</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="BaseBrightnessDialogContainer">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="BrightnessDialogContainer" parent="@style/BaseBrightnessDialogContainer" />

    <style name="Animation" />

    <style name="Animation.ShirtPocketPanel">
        <item name="android:windowEnterAnimation">@*android:anim/grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@*android:anim/shrink_fade_out_from_bottom</item>
    </style>

    <style name="Animation.RecentPanel">
        <item name="android:windowEnterAnimation">@*android:anim/grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@*android:anim/shrink_fade_out_from_bottom</item>
    </style>

    <style name="Animation.NavigationBarFadeIn">
        <item name="android:windowEnterAnimation">@anim/navbar_fade_in</item>
        <item name="android:windowExitAnimation">@null</item>
    </style>

    <!-- Standard animations for hiding and showing the status bar. -->
    <style name="Animation.StatusBar"></style>

    <style name="Theme.SystemUI" parent="@*android:style/Theme.DeviceDefault.QuickSettings">
        <item name="lightIconTheme">@style/DualToneLightTheme</item>
        <item name="darkIconTheme">@style/DualToneDarkTheme</item>
        <item name="wallpaperTextColor">@*android:color/primary_text_material_dark</item>
        <item name="wallpaperTextColorSecondary">@*android:color/secondary_text_material_dark</item>
        <item name="android:colorError">@*android:color/error_color_material_dark</item>
        <item name="android:colorControlHighlight">@*android:color/primary_text_material_dark</item>
        <item name="*android:lockPatternStyle">@style/LockPatternStyle</item>
        <item name="passwordStyle">@style/PasswordTheme</item>

        <!-- Needed for MediaRoute chooser dialog -->
        <item name="*android:isLightTheme">false</item>
    </style>

    <style name="Theme.SystemUI.Light" parent="@*android:style/Theme.DeviceDefault.QuickSettings">
        <item name="wallpaperTextColor">@*android:color/primary_text_material_light</item>
        <item name="wallpaperTextColorSecondary">@*android:color/secondary_text_material_light
        </item>
        <item name="android:colorError">@*android:color/error_color_material_light</item>
        <item name="android:colorControlHighlight">#40000000</item>
        <item name="passwordStyle">@style/PasswordTheme.Light</item>
    </style>

    <style name="LockPatternStyle">
        <item name="*android:regularColor">?attr/wallpaperTextColor</item>
        <item name="*android:successColor">?attr/wallpaperTextColor</item>
        <item name="*android:errorColor">?android:attr/colorError</item>
    </style>

    <!-- Overlay manager may replace this theme -->
    <style name="qs_base" parent="@*android:style/Theme.DeviceDefault.QuickSettings" />

    <style name="qs_theme" parent="qs_base">
        <item name="lightIconTheme">@style/QSIconTheme</item>
        <item name="darkIconTheme">@style/QSIconTheme</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="systemui_theme_remote_input" parent="@android:style/Theme.DeviceDefault.Light">
        <item name="android:colorAccent">@color/remote_input_accent</item>
    </style>

    <style name="Theme.SystemUI.Dialog" parent="@android:style/Theme.DeviceDefault.Light.Dialog" />

    <style name="Theme.SystemUI.Dialog.Alert" parent="@*android:style/Theme.DeviceDefault.Light.Dialog.Alert" />

    <style name="Theme.SystemUI.Dialog.GlobalActions" parent="@android:style/Theme.DeviceDefault.Light.NoActionBar.Fullscreen">
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="QSBorderlessButton">
        <item name="android:padding">12dp</item>
        <item name="android:background">@drawable/qs_btn_borderless_rect</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="TextAppearance.Material.Notification.HeaderTitle" parent="@*android:style/TextAppearance.Material.Notification.Info"></style>

    <style name="SearchPanelCircle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="UserDetailView">
        <item name="numColumns">3</item>
    </style>

    <style name="AutoSizingList">
        <item name="enableAutoSizing">true</item>
    </style>

    <style name="Theme.AlertDialogHost" parent="android:Theme.DeviceDefault">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:alertDialogTheme">@style/Theme.SystemUI.Dialog.Alert</item>
    </style>

    <style name="DualToneLightTheme">
        <item name="backgroundColor">@color/light_mode_icon_color_dual_tone_background</item>
        <item name="fillColor">@color/light_mode_icon_color_dual_tone_fill</item>
        <item name="singleToneColor">@color/light_mode_icon_color_single_tone</item>
    </style>

    <style name="DualToneDarkTheme">
        <item name="backgroundColor">@color/dark_mode_icon_color_dual_tone_background</item>
        <item name="fillColor">@color/dark_mode_icon_color_dual_tone_fill</item>
        <item name="singleToneColor">@color/dark_mode_icon_color_single_tone</item>
    </style>

    <style name="QSIconTheme">
        <item name="backgroundColor">?android:attr/textColorHint</item>
        <item name="fillColor">?android:attr/textColorPrimary</item>
        <item name="singleToneColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="TextAppearance.Volume">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Volume.Header">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="TextAppearance.Volume.Header.Secondary">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>


    <style name="VolumeButtons" parent="@android:style/Widget.Material.Button.Borderless">
        <item name="android:background">@drawable/btn_borderless_rect</item>
    </style>

    <style name="DockedDividerBackground">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">10dp</item>
        <item name="android:layout_gravity">center_vertical</item>
    </style>

    <style name="DockedDividerMinimizedShadow">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">8dp</item>
    </style>

    <style name="DockedDividerHandle">
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:layout_width">96dp</item>
        <item name="android:layout_height">48dp</item>
    </style>

    <style name="TunerSettings" parent="@android:style/Theme.DeviceDefault.Settings">
        <item name="android:windowActionBar">false</item>
        <item name="preferenceTheme">@style/TunerPreferenceTheme</item>
    </style>

    <style name="TunerPreferenceTheme" parent="@style/PreferenceThemeOverlay.SettingsBase"></style>

    <style name="TextAppearance.NotificationInfo">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@android:color/black</item>
    </style>

    <style name="TextAppearance.NotificationInfo.Primary">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:alpha">0.87</item>
    </style>

    <style name="TextAppearance.NotificationInfo.Confirmation">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:alpha">0.87</item>
    </style>

    <style name="TextAppearance.NotificationInfo.Secondary">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:alpha">0.54</item>
    </style>

    <style name="TextAppearance.NotificationInfo.Secondary.Warning">
        <item name="android:textColor">?android:attr/colorError</item>
    </style>

    <style name="TextAppearance.NotificationInfo.Secondary.Link">
        <item name="android:textColor">?android:attr/colorAccent</item>
    </style>

    <style name="TextAppearance.NotificationInfo.Button">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:attr/colorAccent</item>
        <item name="android:background">@drawable/btn_borderless_rect</item>
        <item name="android:gravity">center</item>
        <item name="android:focusable">true</item>
        <item name="android:paddingTop">@dimen/notification_guts_button_vertical_padding</item>
        <item name="android:paddingBottom">@dimen/notification_guts_button_vertical_padding</item>
        <item name="android:paddingLeft">@dimen/notification_guts_button_horizontal_padding</item>
        <item name="android:paddingRight">@dimen/notification_guts_button_horizontal_padding</item>
    </style>

    <style name="TextAppearance.HeadsUpStatusBarText" parent="@*android:style/TextAppearance.Material.Notification.Info"></style>

    <style name="edit_theme" parent="qs_base">
        <item name="android:colorBackground">?android:attr/colorSecondary</item>
    </style>

    <!-- Used to style rotate suggestion button AVD animations -->
    <style name="RotateButtonCCWStart0">
        <item name="rotateButtonStartAngle">0</item>
        <item name="rotateButtonEndAngle">-90</item>
        <item name="rotateButtonScaleX">1</item>
    </style>

    <style name="RotateButtonCCWStart90">
        <item name="rotateButtonStartAngle">90</item>
        <item name="rotateButtonEndAngle">0</item>
        <item name="rotateButtonScaleX">1</item>
    </style>

    <style name="RotateButtonCWStart0">
        <item name="rotateButtonStartAngle">0</item>
        <item name="rotateButtonEndAngle">90</item>
        <item name="rotateButtonScaleX">-1</item>
    </style>

    <style name="RotateButtonCWStart90">
        <item name="rotateButtonStartAngle">90</item>
        <item name="rotateButtonEndAngle">180</item>
        <item name="rotateButtonScaleX">-1</item>
    </style>

    <style name="Animation.Notification" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:windowFullscreen">true</item>
    </style>

</resources>
