<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright (c) 2009, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Name of the status bar as seen in the applications info settings page. [CHAR LIMIT=12] -->
    <string name="app_label">System UI</string>

    <!-- The text for the button in the notification window-shade that clears
         all of the currently visible notifications. [CHAR LIMIT=10]-->
    <string name="status_bar_clear_all_button">Clear</string>

    <!-- Title shown in recents popup for removing an application from the list -->
    <string name="status_bar_recent_remove_item_title">Remove from list</string>

    <!-- Title shown in recents popup for inspecting an application's properties -->
    <string name="status_bar_recent_inspect_item_title">App info</string>

    <!-- Message shown in the middle of the screen after clicking on the recent apps button
         when there are no recent apps to show. Also used for accessibility. [CHAR LIMIT=45]-->
    <string name="status_bar_no_recent_apps">Your recent screens appear here</string>

    <!-- Content description for the button to dismiss Recent Apps (only present on large
         devices) -->
    <string name="status_bar_accessibility_dismiss_recents">Dismiss recent apps</string>

    <!-- Message that is read when you enter recent apps in TalkBack -->
    <plurals name="status_bar_accessibility_recent_apps">
        <item quantity="one">1 screen in Overview</item>
        <item quantity="other">%d screens in Overview</item>
    </plurals>

    <!-- The label in the bar at the top of the status bar when there are no notifications
         showing.  [CHAR LIMIT=40]-->
    <string name="status_bar_no_notifications_title">No notifications</string>

    <!-- The label for the group of notifications for ongoing events in the opened version of
         the status bar.  An ongoing call is the prime example of this.  The MP3 music player
         might be another example.   [CHAR LIMIT=40] -->
    <string name="status_bar_ongoing_events_title">Ongoing</string>

    <!-- The label for the group of notifications for recent events in the opened version of
         the status bar.  Recently received text messsages (SMS), emails, calendar alerts, etc.
         [CHAR LIMIT=40] -->
    <string name="status_bar_latest_events_title">Notifications</string>

    <!-- When the battery is low, this is displayed to the user in a dialog.  The title of the low battery alert.  [CHAR LIMIT=NONE]-->
    <string name="battery_low_title">Battery may run out soon</string>

    <!-- A message that appears when the battery level is getting low in a dialog.  This is
        appended to the subtitle of the low battery alert.  "percentage" is the percentage of battery
        remaining [CHAR LIMIT=none]-->
    <string name="battery_low_percent_format"><xliff:g id="percentage">%s</xliff:g> remaining</string>

    <!-- A message that appears when the battery remaining estimate is low in a dialog.  This is
    appended to the subtitle of the low battery alert.  "percentage" is the percentage of battery
    remaining. "time" is the amount of time remaining before the phone runs out of battery [CHAR LIMIT=none]-->
    <string name="battery_low_percent_format_hybrid"><xliff:g id="percentage">%s</xliff:g> remaining, about <xliff:g id="time">%s</xliff:g> left based on your usage</string>

    <!-- A message that appears when the battery remaining estimate is low in a dialog and insufficient
    data was present to say it is customized to the user. This is appended to the subtitle of the
    low battery alert.  "percentage" is the percentage of battery remaining. "time" is the amount
     of time remaining before the phone runs out of battery [CHAR LIMIT=none]-->
    <string name="battery_low_percent_format_hybrid_short"><xliff:g id="percentage">%s</xliff:g> remaining, about <xliff:g id="time">%s</xliff:g> left</string>

    <!-- Same as battery_low_percent_format, with a notice about battery saver if on. [CHAR LIMIT=none]-->
    <string name="battery_low_percent_format_saver_started"><xliff:g id="percentage">%s</xliff:g> remaining. Battery Saver is on.</string>

    <!-- A message that appears when a USB charger is plugged in and the device does not
    support charging on it.  That is, a charger that fits into the USB port and goes into
    a wall socket, not into a computer. (This happens because some devices require more
    current than the USB spec allows.  [CHAR LIMIT=NONE] -->
    <string name="invalid_charger">Can\'t charge via USB. Use the charger that came with your device.</string>

    <!-- First line of invalid_charger, used in the notification form.  [CHAR LIMIT=NONE]-->
    <string name="invalid_charger_title">Can\'t charge via USB</string>

    <!-- Second line of invalid_charger, used in the notification form.  [CHAR LIMIT=NONE]-->
    <string name="invalid_charger_text">Use the charger that came with your device</string>

    <!-- When the battery is low, this is the label of the button to go to the
         power usage activity to find out what drained the battery.  [CHAR LIMIT=30] -->
    <string name="battery_low_why">Settings</string>

    <!-- Battery saver confirmation dialog title [CHAR LIMIT=NONE]-->
    <string name="battery_saver_confirmation_title">Turn on Battery Saver?</string>

    <!-- Battery saver confirmation dialog ok text [CHAR LIMIT=40]-->
    <string name="battery_saver_confirmation_ok">Turn on</string>

    <!-- Battery saver notification action [CHAR LIMIT=NONE]-->
    <string name="battery_saver_start_action">Turn on Battery Saver</string>

    <!-- Name of the button that links to the Settings app. [CHAR LIMIT=NONE] -->
    <string name="status_bar_settings_settings_button">Settings</string>

    <!-- Name of the button that links to the Wifi settings screen. [CHAR LIMIT=NONE] -->
    <string name="status_bar_settings_wifi_button">Wi-Fi</string>

    <!-- Label in system panel saying the device will use the orientation sensor to rotate [CHAR LIMIT=30] -->
    <string name="status_bar_settings_auto_rotation">Auto-rotate screen</string>

    <!-- Abbreviation / label for mute brightness mode button. Should be all caps. [CHAR LIMIT=6] -->
    <string name="status_bar_settings_mute_label">MUTE</string>

    <!-- Abbreviation / label for automatic brightness mode button. Should be all caps. [CHAR LIMIT=6] -->
    <string name="status_bar_settings_auto_brightness_label">AUTO</string>

    <!-- Label in system panel saying the device will show notifications [CHAR LIMIT=30] -->
    <string name="status_bar_settings_notifications">Notifications</string>

    <!-- Separator for PLMN and SPN in network name. -->
    <string name="status_bar_network_name_separator" translatable="false">|</string>

    <!-- Network connection string for Bluetooth Reverse Tethering -->
    <string name="bluetooth_tethered">Bluetooth tethered</string>
    <!-- Title of a button to open the settings for input methods [CHAR LIMIT=30] -->
    <string name="status_bar_input_method_settings_configure_input_methods">Set up input methods</string>

    <!-- Label of a toggle switch to disable use of the physical keyboard in favor of the IME. [CHAR LIMIT=25] -->
    <string name="status_bar_use_physical_keyboard">Physical keyboard</string>

    <!-- Prompt for the USB device permission dialog [CHAR LIMIT=80] -->
    <string name="usb_device_permission_prompt">Allow <xliff:g id="application">%1$s</xliff:g> to access <xliff:g id="usb_device">%2$s</xliff:g>?</string>

    <!-- Prompt for the USB accessory permission dialog [CHAR LIMIT=80] -->
    <string name="usb_accessory_permission_prompt">Allow <xliff:g id="application">%1$s</xliff:g> to access <xliff:g id="usb_accessory">%2$s</xliff:g>?</string>

    <!-- Prompt for the USB device confirm dialog [CHAR LIMIT=80] -->
    <string name="usb_device_confirm_prompt">Open <xliff:g id="application">%1$s</xliff:g> to handle <xliff:g id="usb_device">%2$s</xliff:g>?</string>

    <!-- Prompt for the USB accessory confirm dialog [CHAR LIMIT=80] -->
    <string name="usb_accessory_confirm_prompt">Open <xliff:g id="application">%1$s</xliff:g> to handle <xliff:g id="usb_accessory">%2$s</xliff:g>?</string>

    <!-- Prompt for the USB accessory URI dialog [CHAR LIMIT=80] -->
    <string name="usb_accessory_uri_prompt">No installed apps work with this USB accessory. Learn more about this accessory at <xliff:g id="url">%1$s</xliff:g></string>

    <!-- Title for USB accessory dialog.  Used when the name of the accessory cannot be determined.  [CHAR LIMIT=50] -->
    <string name="title_usb_accessory">USB accessory</string>

    <!-- View button label for USB dialogs.  [CHAR LIMIT=15] -->
    <string name="label_view">View</string>

    <!-- Checkbox label for USB device dialogs.  [CHAR LIMIT=50] -->
    <string name="always_use_device">Always open <xliff:g id="application">%1$s</xliff:g> when <xliff:g id="usb_device">%2$s</xliff:g> is connected</string>

    <!-- Checkbox label for USB accessory dialogs.  [CHAR LIMIT=50]-->
    <string name="always_use_accessory">Always open <xliff:g id="application">%1$s</xliff:g> when <xliff:g id="usb_accessory">%2$s</xliff:g> is connected</string>

    <!-- Title of confirmation dialog for USB debugging -->
    <string name="usb_debugging_title">Allow USB debugging?</string>

    <!-- Message of confirmation dialog for USB debugging -->
    <string name="usb_debugging_message">The computer\'s RSA key fingerprint is:\n<xliff:g id="fingerprint">%1$s</xliff:g></string>

    <!-- Option to always allow USB debugging from the attached computer -->
    <string name="usb_debugging_always">Always allow from this computer</string>

    <!-- Title of notification shown when trying to enable USB debugging but a secondary user is the current foreground user. -->
    <string name="usb_debugging_secondary_user_title">USB debugging not allowed</string>

    <!-- Message of notification shown when trying to enable USB debugging but a secondary user is the current foreground user. -->
    <string name="usb_debugging_secondary_user_message">The user currently signed in to this device can\'t turn on USB debugging. To use this feature, switch to the primary user.</string>

    <!-- Checkbox label for application compatibility mode ON (zooming app to look like it's running
         on a phone).  [CHAR LIMIT=25] -->
    <string name="compat_mode_on">Zoom to fill screen</string>

    <!-- Checkbox label for application compatibility mode OFF (normal mode on tablets).
         [CHAR LIMIT=25] -->
    <string name="compat_mode_off">Stretch to fill screen</string>

    <!-- Power menu item for taking a screenshot [CHAR LIMIT=20]-->
    <string name="global_action_screenshot">Screenshot</string>

    <!-- Notification ticker displayed when a screenshot is being saved to the Gallery. [CHAR LIMIT=30] -->
    <string name="screenshot_saving_ticker">Saving screenshot\u2026</string>
    <!-- Notification title displayed when a screenshot is being saved to the Gallery. [CHAR LIMIT=50] -->
    <string name="screenshot_saving_title">Saving screenshot\u2026</string>
    <!-- Notification title displayed when a screenshot is saved to the Gallery. [CHAR LIMIT=50] -->
    <string name="screenshot_saved_title">Screenshot saved</string>
    <!-- Notification text displayed when a screenshot is saved to the Gallery. [CHAR LIMIT=100] -->
    <string name="screenshot_saved_text">Tap to view your screenshot</string>
    <!-- Notification title displayed when we fail to take a screenshot. [CHAR LIMIT=50] -->
    <string name="screenshot_failed_title">Couldn\'t save screenshot</string>
    <!-- Notification text displayed when we fail to save a screenshot for unknown reasons. [CHAR LIMIT=100] -->
    <string name="screenshot_failed_to_save_unknown_text">Try taking screenshot again</string>
    <!-- Notification text displayed when we fail to save a screenshot. [CHAR LIMIT=100] -->
    <string name="screenshot_failed_to_save_text">Can\'t save screenshot due to limited storage space</string>
    <!-- Notification text displayed when we fail to take a screenshot. [CHAR LIMIT=100] -->
    <string name="screenshot_failed_to_capture_text">Taking screenshots isn\'t allowed by the app or
        your organization</string>

    <!-- Title for the USB function chooser in UsbPreferenceActivity. [CHAR LIMIT=30] -->
    <string name="usb_preference_title">USB file transfer options</string>
    <!-- Label for the MTP USB function in UsbPreferenceActivity. [CHAR LIMIT=50] -->
    <string name="use_mtp_button_title">Mount as a media player (MTP)</string>
    <!-- Label for the PTP USB function in UsbPreferenceActivity. [CHAR LIMIT=50] -->
    <string name="use_ptp_button_title">Mount as a camera (PTP)</string>
    <!-- Label for the installer CD image option in UsbPreferenceActivity. [CHAR LIMIT=50] -->
    <string name="installer_cd_button_title">Install Android File Transfer app for Mac</string>

    <!-- Content description of the back button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_back">Back</string>
    <!-- Content description of the home button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_home">Home</string>
    <!-- Content description of the menu button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_menu">Menu</string>
    <!-- Content description of the accessibility button in the navigation bar (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_accessibility_button">Accessibility</string>
    <!-- Content description of the rotate button in the navigation bar (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_rotate_button">Rotate screen</string>
    <!-- Content description of the recents button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_recent">Overview</string>
    <!-- Content description of the search button for accessibility. [CHAR LIMIT=NONE] -->
    <string name="accessibility_search_light">Search</string>
    <!-- Content description of the camera button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_camera_button">Camera</string>
    <!-- Content description of the phone button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_phone_button">Phone</string>
    <!-- Content description of the phone button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_voice_assist_button">Voice Assist</string>
    <!-- Content description of the unlock button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_unlock_button">Unlock</string>
    <!-- Content description hint of the unlock button when fingerprint is on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_waiting_for_fingerprint">Waiting for fingerprint</string>
    <!-- Accessibility action of the unlock button when fingerpint is on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_unlock_without_fingerprint">Unlock without using your fingerprint</string>
    <!-- Content description of the Trusted Face icon for accessibility. [CHAR LIMIT=NONE] -->
    <string name="accessibility_scanning_face">Scanning face</string>
    <!-- Click action label for accessibility for the smart reply buttons (not shown on-screen).". [CHAR LIMIT=NONE] -->
    <string name="accessibility_send_smart_reply">Send</string>
    <!-- Click action label for accessibility for the unlock button. [CHAR LIMIT=NONE] -->
    <string name="unlock_label">unlock</string>
    <!-- Click action label for accessibility for the phone button. [CHAR LIMIT=NONE] -->
    <string name="phone_label">open phone</string>
    <!-- Click action label for accessibility for the voice assist button. This is not shown on-screen and is an accessibility label for the icon which launches the voice assist from the lock screen.[CHAR LIMIT=NONE] -->
    <string name="voice_assist_label">open voice assist</string>
    <!-- Click action label for accessibility for the phone button. [CHAR LIMIT=NONE] -->
    <string name="camera_label">open camera</string>
    <!-- Caption for "Recents resize" developer debug feature. [CHAR LIMIT=NONE] -->
    <string name="recents_caption_resize">Select new task layout</string>
    <!-- Button name for "Cancel". [CHAR LIMIT=NONE] -->
    <string name="cancel">Cancel</string>

    <!-- Message shown when the system-provided fingerprint dialog is shown, asking for authentication -->
    <string name="fingerprint_dialog_touch_sensor">Touch the fingerprint sensor</string>
    <!-- Content description of the fingerprint icon when the system-provided fingerprint dialog is showing, for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_fingerprint_dialog_fingerprint_icon">Fingerprint icon</string>
    <!-- Content description of the application icon when the system-provided fingerprint dialog is showing, for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_fingerprint_dialog_app_icon">Application icon</string>
    <!-- Content description for the error/help message are when the system-provided fingerprint dialog is showing, for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_fingerprint_dialog_help_area">Help message area</string>

    <!-- Content description of the compatibility zoom button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_compatibility_zoom_button">Compatibility zoom button.</string>

    <!-- Content description of picture of the compatibility zoom example for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_compatibility_zoom_example">Zoom smaller to larger screen.</string>

    <!-- Content description of the bluetooth icon when connected for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_bluetooth_connected">Bluetooth connected.</string>
    <!-- Content description of the bluetooth icon when connecting for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_bluetooth_disconnected">Bluetooth disconnected.</string>

    <!-- Content description of the battery when no battery for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_no_battery">No battery.</string>
    <!-- Content description of the battery when it is one bar for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_one_bar">Battery one bar.</string>
    <!-- Content description of the battery when it is two bars for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_two_bars">Battery two bars.</string>
     <!-- Content description of the battery when it is three bars for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_three_bars">Battery three bars.</string>
    <!-- Content description of the battery when it is full for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_full">Battery full.</string>

    <!-- Content description of the phone signal when no signal for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_no_phone">No phone.</string>
    <!-- Content description of the phone signal when it is one bar for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_phone_one_bar">Phone one bar.</string>
    <!-- Content description of the phone signal when it is two bars for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_phone_two_bars">Phone two bars.</string>
    <!-- Content description of the phone signal when it is three bars for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_phone_three_bars">Phone three bars.</string>
    <!-- Content description of the phone signal when it is full for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_phone_signal_full">Phone signal full.</string>

    <!-- Content description of the data signal when no signal for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_no_data">No data.</string>
    <!-- Content description of the data signal when it is one bar for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_data_one_bar">Data one bar.</string>
    <!-- Content description of the data signal when it is two bars for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_data_two_bars">Data two bars.</string>
    <!-- Content description of the data signal when it is three bars for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_data_three_bars">Data three bars.</string>
    <!-- Content description of the data signal when it is full for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_data_signal_full">Data signal full.</string>

    <!-- Content description of the wifi label showing what we are connected to. [CHAR LIMIT=NONE] -->
    <string name="accessibility_wifi_name">Connected to <xliff:g id="wifi" example="Home Network">%s</xliff:g>.</string>

    <!-- Content description of the bluetooth label showing what we are connected to. [CHAR LIMIT=NONE] -->
    <string name="accessibility_bluetooth_name">Connected to <xliff:g id="bluetooth" example="Car Audio">%s</xliff:g>.</string>

    <!-- Content description of the cast label showing what we are connected to. [CHAR LIMIT=NONE] -->
    <string name="accessibility_cast_name">Connected to <xliff:g id="cast" example="TV">%s</xliff:g>.</string>


    <!-- Content description of the WiMAX signal when no signal for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_no_wimax">No WiMAX.</string>
    <!-- Content description of the WiMAX signal when it is one bar for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_wimax_one_bar">WiMAX one bar.</string>
    <!-- Content description of the WiMAX signal when it is two bars for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_wimax_two_bars">WiMAX two bars.</string>
    <!-- Content description of the WiMAX signal when it is three bars for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_wimax_three_bars">WiMAX three bars.</string>
    <!-- Content description of the WiMAX signal when it is full for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_wimax_signal_full">WiMAX signal full.</string>

    <!-- Content description of the Ethernet connection when disconnected for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_ethernet_disconnected">Ethernet disconnected.</string>
    <!-- Content description of the Ethernet connection when connected for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_ethernet_connected">Ethernet connected.</string>

    <!-- Content description of an item with no signal for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_no_signal">No signal.</string>
    <!-- Content description of an item with no signal and no connection for accessibility (not shown on the screen) [CHAR LIMIT=NONE] -->
    <string name="accessibility_not_connected">Not connected.</string>
    <!-- Content description of an item with zero signal bars for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_zero_bars">Zero bars.</string>
    <!-- Content description of an item with one signal bar for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_one_bar">One bar.</string>
    <!-- Content description of an item with two signal bars for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_two_bars">Two bars.</string>
    <!-- Content description of an item with three signal bars for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_three_bars">Three bars.</string>
    <!-- Content description of an item with full signal for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_signal_full">Signal full.</string>

    <!-- Content description of an item that is turned on for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_on">On.</string>
    <!-- Content description of an item that is turned off for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_off">Off.</string>
    <!-- Content description of an item that is connected for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_connected">Connected.</string>
    <!-- Content description of an item that is connecting for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_connecting">Connecting.</string>

    <!-- Content description of the data connection type GPRS. [CHAR LIMIT=NONE] -->
    <string name="data_connection_gprs">GPRS</string>

    <!-- Content description of the data connection type HSPA and its variants. [CHAR LIMIT=NONE] -->
    <string name="data_connection_hspa">HSPA</string>

    <!-- Content description of the data connection type 3G. [CHAR LIMIT=NONE] -->
    <string name="data_connection_3g">3G</string>

    <!-- Content description of the data connection type 3.5G. [CHAR LIMIT=NONE] -->
    <string name="data_connection_3_5g">H</string>

    <!-- Content description of the data connection type 3.5G+. [CHAR LIMIT=NONE] -->
    <string name="data_connection_3_5g_plus">H+</string>

    <!-- Content description of the data connection type 4G . [CHAR LIMIT=NONE] -->
    <string name="data_connection_4g">4G</string>

    <!-- Content description of the data connection type 4G+. [CHAR LIMIT=NONE] -->
    <string name="data_connection_4g_plus">4G+</string>

    <!-- Content description of the data connection type LTE. [CHAR LIMIT=NONE] -->
    <string name="data_connection_lte">LTE</string>

    <!-- Content description of the data connection type LTE+. [CHAR LIMIT=NONE] -->
    <string name="data_connection_lte_plus">LTE+</string>

    <!-- Content description of the data connection type CDMA. [CHAR LIMIT=NONE] -->
    <string name="data_connection_cdma">1X</string>

    <!-- Content description of the roaming data connection type. [CHAR LIMIT=NONE] -->
    <string name="data_connection_roaming">Roaming</string>

    <!-- Content description of the data connection type Edge. [CHAR LIMIT=NONE] -->
    <string name="data_connection_edge">EDGE</string>

    <!-- Content description of the data connection type WiFi for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_data_connection_wifi">Wi-Fi</string>

    <!-- Content description of the data connection with no SIM for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_no_sim">No SIM.</string>

    <!-- Content description of the cell data. [CHAR LIMIT=NONE] -->
    <string name="accessibility_cell_data">Mobile Data</string>

    <!-- Content description of the cell data being enabled. [CHAR LIMIT=NONE] -->
    <string name="accessibility_cell_data_on">Mobile Data On</string>

    <!-- Content description of the cell data being disabled. [CHAR LIMIT=NONE] -->
    <string name="cell_data_off_content_description">Mobile data off</string>

    <!-- Content description of the cell data being disabled but shortened. [CHAR LIMIT=20] -->
    <string name="cell_data_off">Off</string>

    <!-- Content description of the bluetooth tethering icon for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_bluetooth_tether">Bluetooth tethering.</string>

    <!-- Content description of the airplane mode icon for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_airplane_mode">Airplane mode.</string>

    <!-- Content description of the VPN icon for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_vpn_on">VPN on.</string>

    <!-- Content description of the no sim icon for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_no_sims">No SIM card.</string>

    <!-- Content description of the carrier network changing icon. [CHAR LIMIT=NONE] -->
    <string name="carrier_network_change_mode">Carrier network changing</string>

    <!-- Content description of button to open battery details icon for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_details">Open battery details</string>

    <!-- Content description of the battery level icon for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_level">Battery <xliff:g id="number">%d</xliff:g> percent.</string>

    <!-- Content description of the battery level icon for accessibility while the device is charging (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_level_charging">Battery charging, <xliff:g id="battery_percentage">%d</xliff:g> percent.</string>

    <!-- Content description of the button for showing a settings panel in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_settings_button">System settings.</string>

    <!-- Content description of the button for showing a notifications panel in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_notifications_button">Notifications.</string>

    <!-- Content description of overflow icon container of the notifications for accessibility (not shown on the screen)[CHAR LIMIT=NONE] -->
    <string name="accessibility_overflow_action">See all notifications</string>

    <!-- Content description of the button for removing a notification in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_remove_notification">Clear notification.</string>

    <!-- Content description of the enabled GPS icon in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_gps_enabled">GPS enabled.</string>

    <!-- Content description of the acquiring GPS icon in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_gps_acquiring">GPS acquiring.</string>

    <!-- Content description of the TeleTypewriter(TTY) enabled icon in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_tty_enabled">TeleTypewriter enabled.</string>

    <!-- Content description of the ringer vibrate icon in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_ringer_vibrate">Ringer vibrate.</string>

    <!-- Content description of the ringer silent icon in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_ringer_silent">Ringer silent.</string>

    <!-- Content description of the cast icon in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_casting">@string/quick_settings_casting</string>

    <!-- Content description of the work mode icon in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_work_mode">@string/quick_settings_work_mode_label</string>

    <!-- Content description to tell the user that this button will remove an application from recents -->
    <string name="accessibility_recents_item_will_be_dismissed">Dismiss <xliff:g id="app" example="Calendar">%s</xliff:g>.</string>
    <!-- Content description to tell the user an application has been removed from recents -->
    <string name="accessibility_recents_item_dismissed"><xliff:g id="app" example="Calendar">%s</xliff:g> dismissed.</string>
    <!-- Content description to tell the user all applications has been removed from recents -->
    <string name="accessibility_recents_all_items_dismissed">All recent applications dismissed.</string>
    <!-- Content description to tell the user that this button will open application info for an application in recents -->
    <string name="accessibility_recents_item_open_app_info">Open <xliff:g id="app" example="Calendar">%s</xliff:g> application info.</string>
    <!-- Content description to tell the user an application has been launched from recents -->
    <string name="accessibility_recents_item_launched">Starting <xliff:g id="app" example="Calendar">%s</xliff:g>.</string>
    <!-- Content description to tell the user a notification has been removed from the notification shade -->
    <string name="accessibility_notification_dismissed">Notification dismissed.</string>

    <!-- Content description for the notification shade panel (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_notification_shade">Notification shade.</string>
    <!-- Content description for the quick settings panel (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_quick_settings">Quick settings.</string>
    <!-- Content description for the lock screen (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_lock_screen">Lock screen.</string>
    <!-- Content description for the settings button in the status bar header. [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_settings">Settings</string>
    <!-- Content description for the recent apps panel (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_recent_apps">Overview.</string>
    <!-- Content description for the work profile lock screen. This prevents work profile apps from being used, but personal apps can be used as normal (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_work_lock">Work lock screen</string>
    <!-- Content description for the close button in the zen mode panel introduction message. [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_close">Close</string>

    <!-- Content description of the wifi tile in quick settings (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_wifi"><xliff:g id="signal" example="Three bars">%1$s</xliff:g>.</string>
    <!-- Announcement made when the wifi is turned off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_wifi_changed_off">Wifi turned off.</string>
    <!-- Announcement made when the wifi is turned on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_wifi_changed_on">Wifi turned on.</string>
    <!-- Content description of the mobile data tile in quick settings (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_mobile">Mobile <xliff:g id="signal" example="Three bars">%1$s</xliff:g>. <xliff:g id="type" example="4G">%2$s</xliff:g>. <xliff:g id="network" example="T-Mobile">%3$s</xliff:g>.</string>
    <!-- Content description of the battery tile in quick settings (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_battery">Battery <xliff:g id="state" example="50% charging">%s</xliff:g>.</string>
    <!-- Content description of the airplane mode tile in quick settings when off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_airplane_off">Airplane mode off.</string>
    <!-- Content description of the airplane mode tile in quick settings when on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_airplane_on">Airplane mode on.</string>
    <!-- Announcement made when the airplane mode changes to off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_airplane_changed_off">Airplane mode turned off.</string>
    <!-- Announcement made when the airplane mode changes to on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_airplane_changed_on">Airplane mode turned on.</string>
    <!-- Content description of the do not disturb tile in quick settings when on in none (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_dnd_none_on">total silence</string>
    <!-- Content description of the do not disturb tile in quick settings when on in alarms only (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_dnd_alarms_on">alarms only</string>
     <!-- Content description of the do not disturb tile in quick settings (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_dnd">Do not disturb.</string>
    <!-- Announcement made when do not disturb changes to off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_dnd_changed_off">Do not disturb turned off.</string>
    <!-- Announcement made when do not disturb changes to on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_dnd_changed_on">Do not disturb turned on.</string>
    <!-- Content description of the bluetooth tile in quick settings (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_bluetooth">Bluetooth.</string>
    <!-- Content description of the bluetooth tile in quick settings when off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_bluetooth_off">Bluetooth off.</string>
    <!-- Content description of the bluetooth tile in quick settings when on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_bluetooth_on">Bluetooth on.</string>
    <!-- Content description of the bluetooth tile in quick settings when connecting (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_bluetooth_connecting">Bluetooth connecting.</string>
    <!-- Content description of the bluetooth tile in quick settings when connected (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_bluetooth_connected">Bluetooth connected.</string>
    <!-- Announcement made when the bluetooth is turned off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_bluetooth_changed_off">Bluetooth turned off.</string>
    <!-- Announcement made when the bluetooth is turned on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_bluetooth_changed_on">Bluetooth turned on.</string>
    <!-- Content description of the location tile in quick settings when off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_location_off">Location reporting off.</string>
    <!-- Content description of the location tile in quick settings when on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_location_on">Location reporting on.</string>
    <!-- Announcement made when the location tile changes to off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_location_changed_off">Location reporting turned off.</string>
    <!-- Announcement made when the location tile changes to on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_location_changed_on">Location reporting turned on.</string>
    <!-- Content description of the alarm tile in quick settings (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_alarm">Alarm set for <xliff:g id="time" example="Wed 3:30 PM">%s</xliff:g>.</string>
    <!-- Content description of quick settings detail panel close button (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_close">Close panel.</string>
    <!-- Content description of zen mode time condition plus button (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_more_time">More time.</string>
    <!-- Content description of zen mode time condition minus button (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_less_time">Less time.</string>
    <!-- Content description of the flashlight tile in quick settings when off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_flashlight_off">Flashlight off.</string>
    <!-- Content description of the flashlight tile in quick settings when unavailable (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_flashlight_unavailable">Flashlight unavailable.</string>
    <!-- Content description of the flashlight tile in quick settings when on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_flashlight_on">Flashlight on.</string>
    <!-- Announcement made when the flashlight state changes to off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_flashlight_changed_off">Flashlight turned off.</string>
    <!-- Announcement made when the flashlight state changes to on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_flashlight_changed_on">Flashlight turned on.</string>
    <!-- Announcement made when the color inversion state changes to off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_color_inversion_changed_off">Color inversion turned off.</string>
    <!-- Announcement made when the color inversion state changes to on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_color_inversion_changed_on">Color inversion turned on.</string>
    <!-- Announcement made when the hotspot state changes to off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_hotspot_changed_off">Mobile hotspot turned off.</string>
    <!-- Announcement made when the hotspot state changes to on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_hotspot_changed_on">Mobile hotspot turned on.</string>
    <!-- Announcement made when the screen stopped casting (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_casting_turned_off">Screen casting stopped.</string>
    <!-- Content description of the work mode title in quick settings when off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_work_mode_off">Work mode off.</string>
    <!-- Content description of the work mode title in quick settings when on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_work_mode_on">Work mode on.</string>
    <!-- Announcement made when the work mode changes to off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_work_mode_changed_off">Work mode turned off.</string>
    <!-- Announcement made when the work mode changes to on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_work_mode_changed_on">Work mode turned on.</string>
    <!-- Announcement made when the Data Saver changes to off (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_data_saver_changed_off">Data Saver turned off.</string>
    <!-- Announcement made when the Data Saver changes to on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_data_saver_changed_on">Data Saver turned on.</string>

    <!-- Content description of the display brightness slider (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_brightness">Display brightness</string>

    <!-- Content description of the charging indicator on Ambient Display (lower-power version of the lock screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_ambient_display_charging">Charging</string>

    <!-- Title of dialog shown when 2G-3G data usage has exceeded limit and has been disabled. [CHAR LIMIT=48] -->
    <string name="data_usage_disabled_dialog_3g_title">2G-3G data is paused</string>
    <!-- Title of dialog shown when 4G data usage has exceeded limit and has been disabled. [CHAR LIMIT=48] -->
    <string name="data_usage_disabled_dialog_4g_title">4G data is paused</string>
    <!-- Title of dialog shown when mobile data usage has exceeded limit and has been disabled. [CHAR LIMIT=48] -->
    <string name="data_usage_disabled_dialog_mobile_title">Mobile data is paused</string>
    <!-- Title of dialog shown when data usage has exceeded limit and has been disabled. [CHAR LIMIT=48] -->
    <string name="data_usage_disabled_dialog_title">Data is paused</string>
    <!-- Body of dialog shown when data usage has exceeded limit and has been disabled. [CHAR LIMIT=NONE] -->
    <string name="data_usage_disabled_dialog">The data limit you set has been reached. You are no longer using mobile data.\n\nIf you resume, charges may apply for data usage.</string>
    <!-- Dialog button indicating that data connection should be re-enabled. [CHAR LIMIT=28] -->
    <string name="data_usage_disabled_dialog_enable">Resume</string>

    <!-- Notification text: when GPS is getting a fix [CHAR LIMIT=50] -->
    <string name="gps_notification_searching_text">Searching for GPS</string>

    <!-- Notification text: when GPS has found a fix [CHAR LIMIT=50] -->
    <string name="gps_notification_found_text">Location set by GPS</string>

    <!-- Accessibility text describing the presence of active location requests by one or more apps -->
    <string name="accessibility_location_active">Location requests active</string>

    <!-- Content description of the clear button in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_clear_all">Clear all notifications.</string>

    <!-- The overflow indicator shown when a group has more notification inside the group than the visible ones. An example is "+ 3" [CHAR LIMIT=5] -->
    <string name="notification_group_overflow_indicator">+ <xliff:g id="number" example="3">%s</xliff:g></string>
    <!-- The overflow indicator shown when a group has more notification inside the group than the visible ones. An example is "New message, +3" [CHAR LIMIT=7] -->
    <string name="notification_group_overflow_indicator_ambient"><xliff:g id="notification_title" example="New message">%s</xliff:g>, +<xliff:g id="overflow" example="+3">%s</xliff:g></string>

    <!-- Content description describing how many more notifications are in a group [CHAR LIMIT=NONE] -->
    <plurals name="notification_group_overflow_description">
        <item quantity="one"><xliff:g id="number" example="1">%s</xliff:g> more notification inside.</item>
        <item quantity="other"><xliff:g id="number" example="3">%s</xliff:g> more notifications inside.</item>
    </plurals>

    <!-- Content description of button in notification inspector for system settings relating to
         notifications from this application [CHAR LIMIT=NONE] -->
    <string name="status_bar_notification_inspect_item_title">Notification settings</string>

    <!-- Content description of button in notification inspetor for application-provided settings
         for its own notifications [CHAR LIMIT=NONE] -->
    <string name="status_bar_notification_app_settings_title"><xliff:g id="app_name" example="Calendar">%s</xliff:g> settings</string>

    <!-- Description of the button in the phone-style notification panel that controls auto-rotation, when auto-rotation is on. [CHAR LIMIT=NONE] -->
    <string name="accessibility_rotation_lock_off">Screen will rotate automatically.</string>

    <!-- Description of the button in the phone-style notification panel that controls auto-rotation, when auto-rotation is off. [CHAR LIMIT=NONE] -->
    <string name="accessibility_rotation_lock_on_landscape">Screen is locked in landscape orientation.</string>

    <!-- Description of the button in the phone-style notification panel that controls auto-rotation, when auto-rotation is off. [CHAR LIMIT=NONE] -->
    <string name="accessibility_rotation_lock_on_portrait">Screen is locked in portrait orientation.</string>

    <!-- Announcement made when the screen is rotating automatically again (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_rotation_lock_off_changed">Screen will now rotate automatically.</string>

    <!-- Announcement made when the rotation lock state changes to landscape only (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_rotation_lock_on_landscape_changed">Screen is now locked in landscape orientation.</string>

    <!-- Announcement made when the rotation lock state changes to portrait only (not shown on the screen).  [CHAR LIMIT=NONE] -->
    <string name="accessibility_rotation_lock_on_portrait_changed">Screen is now locked in portrait orientation.</string>

    <!-- Name of the K-release easter egg: a display case for all our tastiest desserts. [CHAR LIMIT=30] -->
    <string name="dessert_case">Dessert Case</string>

    <!-- Name of the launcher shortcut icon that allows dreams to be started immediately [CHAR LIMIT=20] -->
    <string name="start_dreams">Screen saver</string>

    <!-- Textual description of Ethernet connections -->
    <string name="ethernet_label">Ethernet</string>

    <!-- QuickSettings: Onboarding text that introduces users to long press on an option in order to view the option's menu in Settings [CHAR LIMIT=NONE] -->
    <string name="quick_settings_header_onboarding_text">Touch &amp; hold icons for more options</string>
    <!-- QuickSettings: Do not disturb [CHAR LIMIT=NONE] -->
    <string name="quick_settings_dnd_label">Do not disturb</string>
    <!-- QuickSettings: Do not disturb - Priority only [CHAR LIMIT=NONE] -->
    <string name="quick_settings_dnd_priority_label">Priority only</string>
    <!-- QuickSettings: Do not disturb - Alarms only [CHAR LIMIT=NONE] -->
    <string name="quick_settings_dnd_alarms_label">Alarms only</string>
    <!-- QuickSettings: Do not disturb - Total silence [CHAR LIMIT=NONE] -->
    <string name="quick_settings_dnd_none_label">Total silence</string>
    <!-- QuickSettings: Bluetooth [CHAR LIMIT=NONE] -->
    <string name="quick_settings_bluetooth_label">Bluetooth</string>
    <!-- QuickSettings: Bluetooth (Multiple) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_bluetooth_multiple_devices_label">Bluetooth (<xliff:g id="number">%d</xliff:g> Devices)</string>
    <!-- QuickSettings: Bluetooth (Off) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_bluetooth_off_label">Bluetooth Off</string>
    <!-- QuickSettings: Bluetooth detail panel, text when there are no items [CHAR LIMIT=NONE] -->
    <string name="quick_settings_bluetooth_detail_empty_text">No paired devices available</string>
    <!-- QuickSettings: Bluetooth secondary label for the battery level of a connected device [CHAR LIMIT=20]-->
    <string name="quick_settings_bluetooth_secondary_label_battery_level"><xliff:g id="battery_level_as_percentage">%s</xliff:g> battery</string>
    <!-- QuickSettings: Bluetooth secondary label for an audio device being connected [CHAR LIMIT=20]-->
    <string name="quick_settings_bluetooth_secondary_label_audio">Audio</string>
    <!-- QuickSettings: Bluetooth secondary label for a headset being connected [CHAR LIMIT=20]-->
    <string name="quick_settings_bluetooth_secondary_label_headset">Headset</string>
    <!-- QuickSettings: Bluetooth secondary label for an input/IO device being connected [CHAR LIMIT=20]-->
    <string name="quick_settings_bluetooth_secondary_label_input">Input</string>
    <!-- QuickSettings: Bluetooth secondary label shown when bluetooth is being enabled [CHAR LIMIT=NONE] -->
    <string name="quick_settings_bluetooth_secondary_label_transient">Turning on&#8230;</string>
    <!-- QuickSettings: Brightness [CHAR LIMIT=NONE] -->
    <string name="quick_settings_brightness_label">Brightness</string>
    <!-- QuickSettings: Rotation Unlocked [CHAR LIMIT=NONE] -->
    <string name="quick_settings_rotation_unlocked_label">Auto-rotate</string>
    <!-- Accessibility label for Auto-ratate QuickSettings tile [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_rotation">Auto-rotate screen</string>
    <!-- Accessibility label for value of Auto-ratate QuickSettings tile [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_rotation_value"><xliff:g name="rotation" example="Portrait">%s</xliff:g> mode</string>
    <!-- QuickSettings: Rotation Locked [CHAR LIMIT=NONE] -->
    <string name="quick_settings_rotation_locked_label">Rotation locked</string>
    <!-- QuickSettings: Locked to Portrait [CHAR LIMIT=NONE] -->
    <string name="quick_settings_rotation_locked_portrait_label">Portrait</string>
    <!-- QuickSettings: Locked to Landscape [CHAR LIMIT=NONE] -->
    <string name="quick_settings_rotation_locked_landscape_label">Landscape</string>
    <!-- QuickSettings: IME [CHAR LIMIT=NONE] -->
    <string name="quick_settings_ime_label">Input Method</string>
    <!-- QuickSettings: Location [CHAR LIMIT=NONE] -->
    <string name="quick_settings_location_label">Location</string>
    <!-- QuickSettings: Location (Off) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_location_off_label">Location Off</string>
    <!-- QuickSettings: Media device [CHAR LIMIT=NONE] -->
    <string name="quick_settings_media_device_label">Media device</string>
    <!-- QuickSettings: RSSI [CHAR LIMIT=NONE] -->
    <string name="quick_settings_rssi_label">RSSI</string>
    <!-- QuickSettings: RSSI (No network) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_rssi_emergency_only">Emergency Calls Only</string>
    <!-- QuickSettings: Settings [CHAR LIMIT=NONE] -->
    <string name="quick_settings_settings_label">Settings</string>
    <!-- QuickSettings: Time [CHAR LIMIT=NONE] -->
    <string name="quick_settings_time_label">Time</string>
    <!-- QuickSettings: User [CHAR LIMIT=NONE] -->
    <string name="quick_settings_user_label">Me</string>
    <!-- QuickSettings: Title of the user detail panel [CHAR LIMIT=NONE] -->
    <string name="quick_settings_user_title">User</string>
    <!-- QuickSettings: Label on the item for adding a new user [CHAR LIMIT=NONE] -->
    <string name="quick_settings_user_new_user">New user</string>
    <!-- QuickSettings: Wifi [CHAR LIMIT=NONE] -->
    <string name="quick_settings_wifi_label">Wi-Fi</string>
    <!-- QuickSettings: Wifi (Not connected) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_wifi_not_connected">Not Connected</string>
    <!-- QuickSettings: Wifi (No network) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_wifi_no_network">No Network</string>
    <!-- QuickSettings: Wifi (Off) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_wifi_off_label">Wi-Fi Off</string>
    <!-- QuickSettings: Wifi (On) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_wifi_on_label">Wi-Fi On</string>
    <!-- QuickSettings: Wifi detail panel, text when there are no items [CHAR LIMIT=NONE] -->
    <string name="quick_settings_wifi_detail_empty_text">No Wi-Fi networks available</string>
    <!-- QuickSettings: Wifi secondary label shown when the wifi is being enabled [CHAR LIMIT=NONE] -->
    <string name="quick_settings_wifi_secondary_label_transient">Turning on&#8230;</string>
    <!-- QuickSettings: Cast title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cast_title">Cast</string>
    <!-- QuickSettings: Cast detail panel, status text when casting [CHAR LIMIT=NONE] -->
    <string name="quick_settings_casting">Casting</string>
    <!-- QuickSettings: Cast detail panel, default device name [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cast_device_default_name">Unnamed device</string>
     <!-- QuickSettings: Cast detail panel, default device description [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cast_device_default_description">Ready to cast</string>
    <!-- QuickSettings: Cast detail panel, text when there are no items [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cast_detail_empty_text">No devices available</string>
    <!-- QuickSettings: Brightness dialog title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_brightness_dialog_title">Brightness</string>
    <!-- QuickSettings: Brightness dialog auto brightness button [CHAR LIMIT=NONE] -->
    <string name="quick_settings_brightness_dialog_auto_brightness_label">AUTO</string>
    <!-- QuickSettings: Label for the toggle that controls whether display inversion is enabled. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_inversion_label">Invert colors</string>
    <!-- QuickSettings: Label for the toggle that controls whether display color correction is enabled. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_color_space_label">Color correction mode</string>
    <!-- QuickSettings: Control panel: Label for button that navigates to settings. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_more_settings">More settings</string>
    <!-- QuickSettings: Control panel: Label for button that dismisses control panel. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_done">Done</string>
    <!-- QuickSettings: Control panel: Label for connected device. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_connected">Connected</string>
    <!-- QuickSettings: Control panel: Label for connected device, showing remote device battery level. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_connected_battery_level">Connected, battery <xliff:g id="battery_level_as_percentage">%1$s</xliff:g></string>
    <!-- QuickSettings: Control panel: Label for connecting device. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_connecting">Connecting...</string>
    <!-- QuickSettings: Tethering. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_tethering_label">Tethering</string>
    <!-- QuickSettings: Hotspot. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_hotspot_label">Hotspot</string>
    <!-- QuickSettings: Hotspot. Secondary label shown when the hotspot is being enabled [CHAR LIMIT=NONE] -->
    <string name="quick_settings_hotspot_secondary_label_transient">Turning on&#8230;</string>
    <!-- QuickSettings: Hotspot. Secondary label shown when Data Saver mode is enabled to explain to
         the user why they can't toggle the hotspot tile. [CHAR LIMIT=20] -->
    <string name="quick_settings_hotspot_secondary_label_data_saver_enabled">Data Saver is on</string>
    <!-- QuickSettings: Hotspot: Secondary label for how many devices are connected to the hotspot [CHAR LIMIT=NONE] -->
    <plurals name="quick_settings_hotspot_secondary_label_num_devices">
        <item quantity="one">%d device</item>
        <item quantity="other">%d devices</item>
    </plurals>
    <!-- QuickSettings: Notifications [CHAR LIMIT=NONE] -->
    <string name="quick_settings_notifications_label">Notifications</string>
    <!-- QuickSettings: Flashlight [CHAR LIMIT=NONE] -->
    <string name="quick_settings_flashlight_label">Flashlight</string>
    <!-- QuickSettings: Cellular detail panel title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_title">Mobile data</string>
    <!-- QuickSettings: Cellular detail panel, data usage title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_data_usage">Data usage</string>
    <!-- QuickSettings: Cellular detail panel, remaining data title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_remaining_data">Remaining data</string>
    <!-- QuickSettings: Cellular detail panel, over limit title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_over_limit">Over limit</string>
    <!-- QuickSettings: Cellular detail panel, data used format string [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_data_used"><xliff:g id="data_used" example="2.0 GB">%s</xliff:g> used</string>
    <!-- QuickSettings: Cellular detail panel, data limit format string [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_data_limit"><xliff:g id="data_limit" example="2.0 GB">%s</xliff:g> limit</string>
    <!-- QuickSettings: Cellular detail panel, data warning format string [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_data_warning"><xliff:g id="data_limit" example="2.0 GB">%s</xliff:g> warning</string>
    <!-- QuickSettings: This string is in the easy-to-view settings that a user can pull down from
    the top of their phone's screen. This is a label for a toggle to turn the work profile on and
    off. "Work profile" means a separate profile on a user's phone that's specifically for their
    work apps and managed by their company. "Work" is used as an adjective. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_work_mode_label">Work profile</string>
    <!-- QuickSettings: Label for the toggle to activate Night display (renamed "Night Light" with title caps). [CHAR LIMIT=20] -->
    <string name="quick_settings_night_display_label">Night Light</string>
    <!-- QuickSettings: Secondary text for when the Night Light will be enabled at sunset. [CHAR LIMIT=20] -->
    <string name="quick_settings_night_secondary_label_on_at_sunset">On at sunset</string>
    <!-- QuickSettings: Secondary text for when the Night Light will be on until sunrise. [CHAR LIMIT=20] -->
    <string name="quick_settings_night_secondary_label_until_sunrise">Until sunrise</string>
    <!-- QuickSettings: Secondary text for when the Night Light will be enabled at some user-selected time. [CHAR LIMIT=20] -->
    <string name="quick_settings_night_secondary_label_on_at">On at <xliff:g id="time" example="10 pm">%s</xliff:g></string>
    <!-- QuickSettings: Secondary text for when the Night Light or some other tile will be on until some user-selected time. [CHAR LIMIT=20] -->
    <string name="quick_settings_secondary_label_until">Until <xliff:g id="time" example="7 am">%s</xliff:g></string>

    <!-- QuickSettings: NFC tile [CHAR LIMIT=NONE] -->
    <string name="quick_settings_nfc_label">NFC</string>
    <!-- QuickSettings: NFC (off) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_nfc_off">NFC is disabled</string>
    <!-- QuickSettings: NFC (on) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_nfc_on">NFC is enabled</string>

    <!-- Recents: The empty recents string. [CHAR LIMIT=NONE] -->
    <string name="recents_empty_message">No recent items</string>
    <!-- Recents: The empty recents string after dismissing all tasks. [CHAR LIMIT=NONE] -->
    <string name="recents_empty_message_dismissed_all">You\'ve cleared everything</string>
    <!-- Recents: The info panel app info button string. [CHAR LIMIT=NONE] -->
    <string name="recents_app_info_button_label">Application Info</string>
    <!-- Recents: The screen pinning button. [CHAR LIMIT=NONE] -->
    <string name="recents_lock_to_app_button_label">screen pinning</string>
    <!-- Recents: Temporary string for the button in the recents search bar. [CHAR LIMIT=NONE] -->
    <string name="recents_search_bar_label">search</string>
    <!-- Recents: Launch error string. [CHAR LIMIT=NONE] -->
    <string name="recents_launch_error_message">Could not start <xliff:g id="app" example="Calendar">%s</xliff:g>.</string>
    <!-- Recents: Launch disabled string. [CHAR LIMIT=NONE] -->
    <string name="recents_launch_disabled_message"><xliff:g id="app" example="Calendar">%s</xliff:g> is disabled in safe-mode.</string>
    <!-- Recents: Stack action button string. [CHAR LIMIT=NONE] -->
    <string name="recents_stack_action_button_label">Clear all</string>
    <!-- Recents: Hint text that shows on the drop targets to start multiwindow. [CHAR LIMIT=NONE] -->
    <string name="recents_drag_hint_message">Drag here to use split screen</string>
    <!-- Recents: Text that shows above the navigation bar after launching a few apps. [CHAR LIMIT=NONE] -->
    <string name="recents_swipe_up_onboarding">Swipe up to switch apps</string>
    <!-- Recents: Text that shows above the navigation bar after launching several apps. [CHAR LIMIT=NONE] -->
    <string name="recents_quick_scrub_onboarding">Drag right to quickly switch apps</string>

    <!-- Recents: MultiStack add stack split horizontal radio button. [CHAR LIMIT=NONE] -->
    <string name="recents_multistack_add_stack_dialog_split_horizontal">Split Horizontal</string>
    <!-- Recents: MultiStack add stack split vertical radio button. [CHAR LIMIT=NONE] -->
    <string name="recents_multistack_add_stack_dialog_split_vertical">Split Vertical</string>
    <!-- Recents: MultiStack add stack split custom radio button. [CHAR LIMIT=NONE] -->
    <string name="recents_multistack_add_stack_dialog_split_custom">Split Custom</string>
    <!-- Recents: Accessibility split to the top -->
    <string name="recents_accessibility_split_screen_top">Split screen to the top</string>
    <!-- Recents: Accessibility split to the left -->
    <string name="recents_accessibility_split_screen_left">Split screen to the left</string>
    <!-- Recents: Accessibility split to the right -->
    <string name="recents_accessibility_split_screen_right">Split screen to the right</string>

    <!-- QuickStep: Accessibility to toggle overview [CHAR LIMIT=40] -->
    <string name="quick_step_accessibility_toggle_overview">Toggle Overview</string>

    <!-- Expanded Status Bar Header: Battery Charged [CHAR LIMIT=40] -->
    <string name="expanded_header_battery_charged">Charged</string>

    <!-- Expanded Status Bar Header: Charging, no known time [CHAR LIMIT=40] -->
    <string name="expanded_header_battery_charging">Charging</string>

    <!-- Expanded Status Bar Header: Charging, showing time left until charged [CHAR LIMIT=40] -->
    <string name="expanded_header_battery_charging_with_time"><xliff:g id="charging_time" example="2 hrs 25 min">%s</xliff:g> until full</string>

    <!-- Expanded Status Bar Header: Not charging [CHAR LIMIT=40] -->
    <string name="expanded_header_battery_not_charging">Not charging</string>

    <!-- Shows up when there is a user SSL CA Cert installed on the
         device.  Indicates to the user that SSL traffic can be intercepted.
         If the text fits on one line (~14 chars), it should start with a
         linebreak to position it correctly.  [CHAR LIMIT=45] -->
    <string name="ssl_ca_cert_warning">Network may\nbe monitored</string>

    <!-- Description of the unlock target in the Slide unlock screen. [CHAR LIMIT=NONE] -->
    <string name="description_target_search">Search</string>
    <!-- Description of the up direction in which one can to slide the handle in the Slide unlock screen. [CHAR LIMIT=NONE] -->
    <string name="description_direction_up">Slide up for <xliff:g id="target_description" example="Unlock">%s</xliff:g>.</string>
    <!-- Description of the left direction in which one can to slide the handle in the Slide unlock screen. [CHAR LIMIT=NONE] -->
    <string name="description_direction_left">"Slide left for <xliff:g id="target_description" example="Unlock">%s</xliff:g>.</string>

    <!-- Zen mode: Priority only introduction message on first use -->
    <string name="zen_priority_introduction">You won\'t be disturbed by sounds and vibrations, except from alarms, reminders, events, and callers you specify. You\'ll still hear anything you choose to play including music, videos, and games.</string>

    <!-- Zen mode: Alarms only introduction message on first use -->
    <string name="zen_alarms_introduction">You won\'t be disturbed by sounds and vibrations, except from alarms. You\'ll still hear anything you choose to play including music, videos, and games.</string>

    <!-- Zen mode: Priority only customization button label -->
    <string name="zen_priority_customize_button">Customize</string>

    <!-- Zen mode: Total silence introduction message on first use (voice capable devices) -->
    <string name="zen_silence_introduction_voice">This blocks ALL sounds and vibrations, including from alarms, music, videos, and games. You\'ll still be able to make phone calls.</string>

    <!-- Zen mode: Total silence introduction message on first use (non-voice capable devices) -->
    <string name="zen_silence_introduction">This blocks ALL sounds and vibrations, including from alarms, music, videos, and games.</string>

    <!-- Text for overflow card on Keyguard when there is not enough space for all notifications on Keyguard. [CHAR LIMIT=1] -->
    <string name="keyguard_more_overflow_text">+<xliff:g id="number_of_notifications" example="5">%d</xliff:g></string>

    <!-- An explanation for the visual speed bump in the notifications, which will appear when you click on it. [CHAR LIMIT=50] -->
    <string name="speed_bump_explanation">Less urgent notifications below</string>

    <!-- Shows to explain the double tap interaction with notifications: After tapping a notification on Keyguard, this will explain users to tap again to launch a notification. [CHAR LIMIT=60] -->
    <string name="notification_tap_again">Tap again to open</string>

    <!-- Shows when people have pressed the unlock icon to explain how to unlock. [CHAR LIMIT=60] -->
    <string name="keyguard_unlock">Swipe up to unlock</string>

    <!-- Text on keyguard screen and in Quick Settings footer indicating that the device is enterprise-managed by a Device Owner [CHAR LIMIT=60] -->
    <string name="do_disclosure_generic">This device is managed by your organization</string>

    <!-- Text on keyguard screen and in Quick Settings footer indicating that the device is enterprise-managed by a Device Owner [CHAR LIMIT=40] -->
    <string name="do_disclosure_with_name">This device is managed by <xliff:g id="organization_name" example="Foo, Inc.">%s</xliff:g></string>

    <!-- Shows when people have clicked on the phone icon [CHAR LIMIT=60] -->
    <string name="phone_hint">Swipe from icon for phone</string>

    <!-- Shows when people have clicked on the voice assist icon [CHAR LIMIT=60] -->
    <string name="voice_hint">Swipe from icon for voice assist</string>

    <!-- Shows when people have clicked on the camera icon [CHAR LIMIT=60] -->
    <string name="camera_hint">Swipe from icon for camera</string>

    <!-- Accessibility content description for Interruption level: None. [CHAR LIMIT=NONE] -->
    <string name="interruption_level_none_with_warning">Total silence.  This will also silence screen readers.</string>

    <!-- Interruption level: None. [CHAR LIMIT=40] -->
    <string name="interruption_level_none">Total silence</string>

    <!-- Interruption level: Priority. [CHAR LIMIT=40] -->
    <string name="interruption_level_priority">Priority only</string>

    <!-- Interruption level: Alarms only. [CHAR LIMIT=40] -->
    <string name="interruption_level_alarms">Alarms only</string>

    <!-- Interruption level: None.  Optimized for narrow two-line display. [CHAR LIMIT=40] -->
    <string name="interruption_level_none_twoline">Total\nsilence</string>

    <!-- Interruption level: Priority.  Optimized for narrow two-line display. [CHAR LIMIT=40] -->
    <string name="interruption_level_priority_twoline">Priority\nonly</string>

    <!-- Interruption level: Alarms only.  Optimized for narrow two-line display. [CHAR LIMIT=40] -->
    <string name="interruption_level_alarms_twoline">Alarms\nonly</string>

    <!-- Indication on the keyguard that is shown when the device is charging. [CHAR LIMIT=50]-->
    <string name="keyguard_indication_charging_time"><xliff:g id="percentage">%2$s</xliff:g> • Charging (<xliff:g id="charging_time_left" example="4 hours and 2 minutes">%s</xliff:g> until full)</string>

    <!-- Indication on the keyguard that is shown when the device is charging rapidly. Should match keyguard_plugged_in_charging_fast [CHAR LIMIT=50]-->
    <string name="keyguard_indication_charging_time_fast"><xliff:g id="percentage">%2$s</xliff:g> • Charging rapidly (<xliff:g id="charging_time_left" example="4 hours and 2 minutes">%s</xliff:g> until full)</string>

    <!-- Indication on the keyguard that is shown when the device is charging slowly. Should match keyguard_plugged_in_charging_slowly [CHAR LIMIT=50]-->
    <string name="keyguard_indication_charging_time_slowly"><xliff:g id="percentage">%2$s</xliff:g> • Charging slowly (<xliff:g id="charging_time_left" example="4 hours and 2 minutes">%s</xliff:g> until full)</string>

    <!-- Related to user switcher --><skip/>

    <!-- Accessibility label for the button that opens the user switcher. -->
    <string name="accessibility_multi_user_switch_switcher">Switch user</string>

    <!-- Accessibility label for the button that opens the user switcher and announces the current user. -->
    <string name="accessibility_multi_user_switch_switcher_with_current">Switch user, current user <xliff:g id="current_user_name" example="John Doe">%s</xliff:g></string>

    <!-- Accessibility label for the user icon on the lock screen. -->
    <string name="accessibility_multi_user_switch_inactive">Current user <xliff:g id="current_user_name" example="John Doe">%s</xliff:g></string>

    <!-- Accessibility label for the button that opens the quick contact of the user. -->
    <string name="accessibility_multi_user_switch_quick_contact">Show profile</string>

    <!-- Label for the adding a new user in the user switcher [CHAR LIMIT=35] -->
    <string name="user_add_user">Add user</string>

    <!-- Name for a freshly added user [CHAR LIMIT=30] -->
    <string name="user_new_user_name">New user</string>

    <!-- Name for the guest user [CHAR LIMIT=35] -->
    <string name="guest_nickname">Guest</string>

    <!-- Label for adding a new guest in the user switcher [CHAR LIMIT=35] -->
    <string name="guest_new_guest">Add guest</string>

    <!-- Label for exiting and removing the  guest session in the user switcher [CHAR LIMIT=35] -->
    <string name="guest_exit_guest">Remove guest</string>

    <!-- Title of the confirmation dialog when exiting guest session [CHAR LIMIT=NONE] -->
    <string name="guest_exit_guest_dialog_title">Remove guest?</string>

    <!-- Message of the confirmation dialog when exiting guest session [CHAR LIMIT=NONE] -->
    <string name="guest_exit_guest_dialog_message">All apps and data in this session will be deleted.</string>

    <!-- Label for button in confirmation dialog when exiting guest session [CHAR LIMIT=35] -->
    <string name="guest_exit_guest_dialog_remove">Remove</string>

    <!-- Title of the notification when resuming an existing guest session [CHAR LIMIT=NONE] -->
    <string name="guest_wipe_session_title">Welcome back, guest!</string>

    <!-- Message of the notification when resuming an existing guest session [CHAR LIMIT=NONE] -->
    <string name="guest_wipe_session_message">Do you want to continue your session?</string>

    <!-- Notification when resuming an existing guest session: Action that starts a new session [CHAR LIMIT=35] -->
    <string name="guest_wipe_session_wipe">Start over</string>

    <!-- Notification when resuming an existing guest session: Action that continues with the current session [CHAR LIMIT=35] -->
    <string name="guest_wipe_session_dontwipe">Yes, continue</string>

    <!-- Title of the notification shown to a new guest user [CHAR LIMIT=60] -->
    <string name="guest_notification_title">Guest user</string>

    <!-- Text of the notification shown to a new guest user [CHAR LIMIT=60] -->
    <string name="guest_notification_text">To delete apps and data, remove guest user</string>

    <!-- Remove action in the notification shown to a new guest user [CHAR LIMIT=30] -->
    <string name="guest_notification_remove_action">REMOVE GUEST</string>

    <!-- Title of the notification shown to logout the current user [CHAR LIMIT=60] -->
    <string name="user_logout_notification_title">Logout user</string>

    <!-- Text of the notification shown to logout the current user [CHAR LIMIT=60] -->
    <string name="user_logout_notification_text">Logout current user</string>

    <!-- Logout action in the notification shown to logout the current user [CHAR LIMIT=30] -->
    <string name="user_logout_notification_action">LOGOUT USER</string>

    <!-- Title for add user confirmation dialog [CHAR LIMIT=30] -->
    <string name="user_add_user_title" msgid="2108112641783146007">Add new user?</string>

    <!-- Message for add user confirmation dialog - short version. [CHAR LIMIT=none] -->
    <string name="user_add_user_message_short" msgid="1511354412249044381">When you add a new user, that person needs to set up their space.\n\nAny user can update apps for all other users. </string>

    <!-- Title of the confirmation dialog for deleting a user [CHAR LIMIT=NONE] -->
    <string name="user_remove_user_title">Remove user?</string>

    <!-- Message of the confirmation dialog for deleting a user [CHAR LIMIT=NONE] -->
    <string name="user_remove_user_message">All apps and data of this user will be deleted.</string>

    <!-- Label for button in confirmation dialog when exiting guest session [CHAR LIMIT=35] -->
    <string name="user_remove_user_remove">Remove</string>

    <!-- Battery saver notification title. [CHAR LIMIT=60]-->
    <string name="battery_saver_notification_title">Battery Saver is on</string>

    <!-- Battery saver notification text. [CHAR LIMIT=60] -->
    <string name="battery_saver_notification_text">Reduces performance and background data</string>

    <!-- Battery saver notification action text. [CHAR LIMIT=60] -->
    <string name="battery_saver_notification_action_text">Turn off Battery Saver</string>

    <!-- Media projection permission dialog warning text. [CHAR LIMIT=NONE] -->
    <string name="media_projection_dialog_text"><xliff:g id="app_seeking_permission" example="Hangouts">%s</xliff:g> will start capturing everything that\'s displayed on your screen.</string>

    <!-- Media projection permission dialog permanent grant check box. [CHAR LIMIT=NONE] -->
    <string name="media_projection_remember_text">Don\'t show again</string>

    <!-- The text to clear all notifications. [CHAR LIMIT=60] -->
    <string name="clear_all_notifications_text">Clear all</string>

    <!-- The text for the manage notifications link. [CHAR LIMIT=40] -->
    <string name="manage_notifications_text">Manage notifications</string>

    <!-- The text to show in the notifications shade when dnd is suppressing notifications. [CHAR LIMIT=100] -->
    <string name="dnd_suppressing_shade_text">Notifications paused by Do Not Disturb</string>

    <!-- Media projection permission dialog action text. [CHAR LIMIT=60] -->
    <string name="media_projection_action_text">Start now</string>

    <!-- Text which is shown in the notification shade when there are no notifications. [CHAR LIMIT=30] -->
    <string name="empty_shade_text">No notifications</string>

    <!-- Footer profile owned text [CHAR LIMIT=50] -->
    <string name="profile_owned_footer">Profile may be monitored</string>

    <!-- Footer vpn present text [CHAR LIMIT=50] -->
    <string name="vpn_footer">Network may be monitored</string>

    <!-- Footer vpn present text [CHAR LIMIT=50] -->
    <string name="branded_vpn_footer">Network may be monitored</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a device owner that can monitor the network traffic [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_management_monitoring">Your organization manages this device and may monitor network traffic</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a device owner that can monitor the network traffic [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_named_management_monitoring"><xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g> manages this device and may monitor network traffic</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a device owner and is connected to a VPN [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_management_named_vpn">Device is managed by your organization and connected to <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g></string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a device owner and is connected to a VPN [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_named_management_named_vpn">Device is managed by <xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g> and connected to <xliff:g id="vpn_app" example="Foo VPN App">%2$s</xliff:g></string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a device owner [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_management">Device is managed by your organization</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a device owner [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_named_management">Device is managed by <xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g></string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a device owner and is connected to two VPNs, one in the current user, one in the managed profile [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_management_vpns">Device is managed by your organization and connected to VPNs</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a device owner and is connected to two VPNs, one in the current user, one in the managed profile [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_named_management_vpns">Device is managed by <xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g> and connected to VPNs</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a managed profile which can be monitored by the profile owner [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_managed_profile_monitoring">Your organization may monitor network traffic in your work profile</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a managed profile which can be monitored by the profile owner [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_named_managed_profile_monitoring"><xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g> may monitor network traffic in your work profile</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that a certificate authorithy is installed on this device and the traffic might be monitored [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_monitoring">Network may be monitored</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device is connected to two VPNs, one in the current user, one in the managed profile [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_vpns">Device connected to VPNs</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device is connected to a VPN in the work profile [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_managed_profile_named_vpn">Work profile connected to <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g></string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device is connected to a VPN in the personal profile (as opposed to the work profile) [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_personal_profile_named_vpn">Personal profile connected to <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g></string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device is connected to a VPN [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_named_vpn">Device connected to <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g></string>

    <!-- Monitoring dialog title for device owned devices [CHAR LIMIT=35] -->
    <string name="monitoring_title_device_owned">Device management</string>

    <!-- Monitoring dialog title for profile owned devices [CHAR LIMIT=35] -->
    <string name="monitoring_title_profile_owned">Profile monitoring</string>

    <!-- Monitoring dialog title for normal devices  [CHAR LIMIT=35]-->
    <string name="monitoring_title">Network monitoring</string>

    <!-- STOPSHIP Monitoring strings still need to be finalized and approved -->
    <!-- Monitoring dialog subtitle for the section describing VPN [CHAR LIMIT=35]-->
    <string name="monitoring_subtitle_vpn">VPN</string>

    <!-- Monitoring dialog subtitle for the section describing network logging [CHAR LIMIT=35]-->
    <string name="monitoring_subtitle_network_logging">Network logging</string>

    <!-- Monitoring dialog subtitle for the section describing certificate authorities [CHAR LIMIT=35]-->
    <string name="monitoring_subtitle_ca_certificate">CA certificates</string>

    <!-- Monitoring dialog disable vpn button [CHAR LIMIT=30] -->
    <string name="disable_vpn">Disable VPN</string>

    <!-- Monitoring dialog disconnect vpn button [CHAR LIMIT=30] -->
    <string name="disconnect_vpn">Disconnect VPN</string>

    <!-- Monitoring dialog label for button opening a page with more information on the admin's abilities [CHAR LIMIT=30] -->
    <string name="monitoring_button_view_policies">View Policies</string>

    <!-- Monitoring dialog: Description of the device owner by name. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_named_management">Your device is managed by <xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g>.\n\nYour admin can monitor and manage settings, corporate access, apps, data associated with your device, and your device\'s location information.\n\nFor more information, contact your admin.</string>

    <!-- Monitoring dialog: Description of a device owner. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_management">Your device is managed by your organization.\n\nYour admin can monitor and manage settings, corporate access, apps, data associated with your device, and your device\'s location information.\n\nFor more information, contact your admin.</string>

    <!-- Monitoring dialog: Description of a CA Certificate. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_management_ca_certificate">Your organization installed a certificate authority on this device. Your secure network traffic may be monitored or modified.</string>

    <!-- Monitoring dialog: Description of a CA Certificate in the work profile. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_managed_profile_ca_certificate">Your organization installed a certificate authority in your work profile. Your secure network traffic may be monitored or modified.</string>

    <!-- Monitoring dialog: Description of a CA Certificate. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_ca_certificate">A certificate authority is installed on this device. Your secure network traffic may be monitored or modified.</string>

    <!-- Monitoring dialog: Description of Network Logging. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_management_network_logging">Your admin has turned on network logging, which monitors traffic on your device.</string>

    <!-- Monitoring dialog: Description of an active VPN. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_named_vpn">You\'re connected to <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g>, which can monitor your network activity, including emails, apps, and websites.</string>

    <!-- Monitoring dialog: Description of two active VPNs. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_two_named_vpns">You\'re connected to <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g> and <xliff:g id="vpn_app" example="Bar VPN App">%2$s</xliff:g>, which can monitor your network activity, including emails, apps, and websites.</string>

    <!-- Monitoring dialog: Description of an active VPN in the work profile. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_managed_profile_named_vpn">Your work profile is connected to <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g>, which can monitor your network activity, including emails, apps, and websites.</string>

    <!-- Monitoring dialog: Description of an active VPN in the personal profile (as opposed to the work profile). [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_personal_profile_named_vpn">Your personal profile is connected to <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g>, which can monitor your network activity, including emails, apps, and websites.</string>

    <!-- Monitoring dialog: Header indicating that the device is managed by a Device Owner app [CHAR LIMIT=80] -->
    <string name="monitoring_description_do_header_generic">Your device is managed by <xliff:g id="device_owner_app" example="Google Mobile Management">%1$s</xliff:g>.</string>

    <!-- Monitoring dialog: Header indicating that the device is managed by a Device Owner app [CHAR LIMIT=60] -->
    <string name="monitoring_description_do_header_with_name"><xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g> uses <xliff:g id="device_owner_app" example="Google Mobile Management">%2$s</xliff:g> to manage your device.</string>

    <!-- Monitoring dialog: Part of text body explaining what a Device Owner app can do [CHAR LIMIT=130] -->
    <string name="monitoring_description_do_body">Your admin can monitor and manage settings, corporate access, apps, data associated with your device, and your device\'s location information.</string>

    <!-- Monitoring dialog: Space that separates the body text and the "learn more" link that follows it. [CHAR LIMIT=5] -->
    <string name="monitoring_description_do_learn_more_separator">" "</string>

    <!-- Monitoring dialog: Link to learn more about what a Device Owner app can do [CHAR LIMIT=55] -->
    <string name="monitoring_description_do_learn_more">Learn more</string>

    <!-- Monitoring dialog: Part of text body explaining that a VPN is connected and what it can do, for devices managed by a Device Owner app [CHAR LIMIT=130] -->
    <string name="monitoring_description_do_body_vpn">You\'re connected to <xliff:g id="vpn_app">%1$s</xliff:g>, which can monitor your network activity, including emails, apps, and websites.</string>

    <!-- Monitoring dialog: Space that separates the VPN body text and the "Open VPN Settings" link that follows it. [CHAR LIMIT=5] -->
    <string name="monitoring_description_vpn_settings_separator">" "</string>

    <!-- Monitoring dialog: Link to open the VPN settings page [CHAR LIMIT=60] -->
    <string name="monitoring_description_vpn_settings">Open VPN settings</string>

    <!-- Monitoring dialog: Space that separates the CA certs body text and the "Open trusted credentials" link that follows it. [CHAR LIMIT=5] -->
    <string name="monitoring_description_ca_cert_settings_separator">" "</string>

    <!-- Monitoring dialog: Link to open the settings page containing CA certificates [CHAR LIMIT=NONE] -->
    <string name="monitoring_description_ca_cert_settings">Open trusted credentials</string>

    <!-- Monitoring dialog: Network logging text [CHAR LIMIT=400] -->
    <string name="monitoring_description_network_logging">Your admin has turned on network logging, which monitors traffic on your device.\n\nFor more information, contact your admin.</string>

    <!-- Monitoring dialog VPN text [CHAR LIMIT=400] -->
    <string name="monitoring_description_vpn">You gave an app permission to set up a VPN connection.\n\nThis app can monitor your device and network activity, including emails, apps, and websites.</string>

    <!-- Monitoring dialog VPN with profile owner text [CHAR LIMIT=400] -->
    <string name="monitoring_description_vpn_profile_owned">Your work profile is managed by <xliff:g id="organization">%1$s</xliff:g>.\n\nYour admin is capable of monitoring your network activity including emails, apps, and websites.\n\nFor more information, contact your admin.\n\nYou\'re also connected to a VPN, which can monitor your network activity.</string>

    <!-- Name for a generic legacy VPN connection [CHAR LIMIT=20] -->
    <string name="legacy_vpn_name">VPN</string>

    <!-- Monitoring dialog text for single app (no profile or device owner) [CHAR LIMIT=400] -->
    <string name="monitoring_description_app">You\'re connected to
        <xliff:g id="application">%1$s</xliff:g>, which can monitor your network activity,
        including emails, apps, and websites.</string>

    <!-- Monitoring dialog text for single app (inside personal profile) [CHAR LIMIT=400] -->
    <string name="monitoring_description_app_personal">You\'re connected to <xliff:g id="application">%1$s</xliff:g>, which can monitor your personal network activity, including emails, apps, and websites.</string>

    <!-- Monitoring dialog text for single app (inside personal profile) [CHAR LIMIT=400] -->
    <string name="branded_monitoring_description_app_personal">You\'re connected to <xliff:g id="application">%1$s</xliff:g>, which can monitor your personal network activity, including emails, apps, and websites.</string>

    <!-- Monitoring dialog text for single app (inside work profile) [CHAR LIMIT=400] -->
    <string name="monitoring_description_app_work">Your work profile is managed by
        <xliff:g id="organization">%1$s</xliff:g>. The profile is connected to
        <xliff:g id="application">%2$s</xliff:g>, which can monitor your work network activity,
        including emails, apps, and websites.\n\nFor more information, contact your admin.</string>

    <!-- Monitoring dialog text for multiple apps (in personal and work profiles) [CHAR LIMIT=400] -->
    <string name="monitoring_description_app_personal_work">Your work profile is managed by
        <xliff:g id="organization">%1$s</xliff:g>. The profile is connected to
        <xliff:g id="application_work">%2$s</xliff:g>, which can monitor your work network activity,
        including emails, apps, and websites.\n\nYou\'re also connected to
        <xliff:g id="application_personal">%3$s</xliff:g>, which can monitor your personal network
        activity.</string>

    <!-- Indication on the keyguard that appears when a trust agents unlocks the device. [CHAR LIMIT=40] -->
    <string name="keyguard_indication_trust_granted">Unlocked for <xliff:g id="user_name" example="John Doe">%1$s</xliff:g></string>

    <!-- Indication on the keyguard that appears when a trust agent is active and available. [CHAR LIMIT=40] -->
    <string name="keyguard_indication_trust_managed"><xliff:g id="trust_agent" example="Smart Lock">%1$s</xliff:g> is running</string>

    <!-- Indication on the keyguard that appears when the user disables trust agents until the next time they unlock manually. [CHAR LIMIT=NONE] -->
    <string name="keyguard_indication_trust_disabled">Device will stay locked until you manually unlock</string>

    <!-- Title of notification educating the user about enabling notifications on the lockscreen. [CHAR LIMIT=40] -->
    <string name="hidden_notifications_title">Get notifications faster</string>

    <!-- Body of notification educating the user about enabling notifications on the lockscreen. [CHAR LIMIT=60] -->
    <string name="hidden_notifications_text">See them before you unlock</string>

    <!-- Cancel action for notification educating the user about enabling notifications on the lockscreen. [CHAR LIMIT=20] -->
    <string name="hidden_notifications_cancel">No thanks</string>

    <!-- continue action for notification educating the user about enabling notifications on the lockscreen. [CHAR LIMIT=20] -->
    <string name="hidden_notifications_setup">Set up</string>

    <!-- Accessibility string for current zen mode and selected exit condition. A template that simply concatenates existing mode string and the current condition description. [CHAR LIMIT=20] -->
    <string name="zen_mode_and_condition"><xliff:g id="zen_mode" example="Priority interruptions only">%1$s</xliff:g>. <xliff:g id="exit_condition" example="For one hour">%2$s</xliff:g></string>

    <!-- Button label for ending zen mode in the volume dialog -->
    <string name="volume_zen_end_now">Turn off now</string>

    <!-- Content description for accessibility (not shown on the screen): volume dialog settings button. [CHAR LIMIT=NONE] -->
    <string name="accessibility_volume_settings">Sound settings</string>

    <!-- Content description for accessibility (not shown on the screen): volume dialog expand button. [CHAR LIMIT=NONE] -->
    <string name="accessibility_volume_expand">Expand</string>

    <!-- Content description for accessibility (not shown on the screen): volume dialog collapse button. [CHAR LIMIT=NONE] -->
    <string name="accessibility_volume_collapse">Collapse</string>

    <!-- content description for audio output chooser [CHAR LIMIT=NONE]-->
    <string name="accessibility_output_chooser">Switch output device</string>

    <!-- Screen pinning dialog title. -->
    <string name="screen_pinning_title">Screen is pinned</string>
    <!-- Screen pinning dialog description. -->
    <string name="screen_pinning_description">This keeps it in view until you unpin. Touch &amp; hold Back and Overview to unpin.</string>
    <string name="screen_pinning_description_recents_invisible">This keeps it in view until you unpin. Touch &amp; hold Back and Home to unpin.</string>
    <!-- Screen pinning dialog description. -->
    <string name="screen_pinning_description_accessible">This keeps it in view until you unpin. Touch &amp; hold Overview to unpin.</string>
    <string name="screen_pinning_description_recents_invisible_accessible">This keeps it in view until you unpin. Touch &amp; hold Home to unpin.</string>
    <!-- Notify use that they are in Lock-to-app -->
    <string name="screen_pinning_toast">To unpin this screen, touch &amp; hold Back and Overview
        buttons</string>
    <string name="screen_pinning_toast_recents_invisible">To unpin this screen, touch &amp; hold Back
        and Home buttons</string>
    <!-- Screen pinning positive response. -->
    <string name="screen_pinning_positive">Got it</string>
    <!-- Screen pinning negative response. -->
    <string name="screen_pinning_negative">No thanks</string>
    <!-- Enter/Exiting screen pinning indication. -->
    <string name="screen_pinning_start">Screen pinned</string>
    <string name="screen_pinning_exit">Screen unpinned</string>


    <!-- Hide quick settings tile confirmation title -->
    <string name="quick_settings_reset_confirmation_title">Hide <xliff:g id="tile_label" example="Hotspot">%1$s</xliff:g>?</string>

    <!-- Hide quick settings tile confirmation message -->
    <string name="quick_settings_reset_confirmation_message">It will reappear the next time you turn it on in settings.</string>

    <!-- Hide quick settings tile confirmation button -->
    <string name="quick_settings_reset_confirmation_button">Hide</string>

    <!-- Toast shown when user unlocks screen and managed profile activity is in the foreground -->
    <string name="managed_profile_foreground_toast">You\'re using your work profile</string>

    <!-- volume stream names. All nouns. -->
    <string name="stream_voice_call">Call</string> <!-- STREAM_VOICE_CALL -->
    <string name="stream_system">System</string> <!-- STREAM_SYSTEM -->
    <string name="stream_ring">Ring</string> <!-- STREAM_RING -->
    <string name="stream_music">Media</string> <!-- STREAM_MUSIC -->
    <string name="stream_alarm">Alarm</string> <!-- STREAM_ALARM -->
    <string name="stream_notification">Notification</string> <!-- STREAM_NOTIFICATION -->
    <string name="stream_bluetooth_sco">Bluetooth</string> <!-- STREAM_BLUETOOTH_SCO -->
    <string name="stream_system_enforced" translatable="false">System enforced</string> <!-- STREAM_SYSTEM_ENFORCED -->
    <string name="stream_dtmf">Dual multi tone frequency</string> <!-- STREAM_DTMF -->
    <string name="stream_tts" translatable="false">Transmitted Through Speaker</string> <!-- STREAM_TTS -->
    <string name="stream_accessibility">Accessibility</string> <!-- STREAM_ACCESSIBILITY -->

    <string name="ring_toggle_title">Calls</string>
    <string name="volume_ringer_status_normal">Ring</string>
    <string name="volume_ringer_status_vibrate">Vibrate</string>
    <string name="volume_ringer_status_silent">Mute</string>

    <!-- Shown in the header of quick settings to indicate to the user that their phone ringer is on vibrate. [CHAR_LIMIT=NONE] -->
    <string name="qs_status_phone_vibrate">Phone on vibrate</string>
    <!-- Shown in the header of quick settings to indicate to the user that their phone ringer is on silent (muted). [CHAR_LIMIT=NONE] -->
    <string name="qs_status_phone_muted">Phone muted</string>

    <string name="volume_stream_muted" translatable="false">%s silent</string>
    <string name="volume_stream_vibrate" translatable="false">%s vibrate</string>
    <string name="volume_stream_suppressed" translatable="false">%1$s silent — %2$s</string>
    <string name="volume_stream_muted_dnd" translatable="false">%s silent — Total silence</string>
    <string name="volume_stream_limited_dnd" translatable="false">%s — Priority only</string>
    <string name="volume_stream_vibrate_dnd" translatable="false">%s vibrate — Priority only</string>

    <string name="volume_stream_content_description_unmute">%1$s. Tap to unmute.</string>
    <string name="volume_stream_content_description_vibrate">%1$s. Tap to set to vibrate. Accessibility services may be muted.</string>
    <string name="volume_stream_content_description_mute">%1$s. Tap to mute. Accessibility services may be muted.</string>
    <string name="volume_stream_content_description_vibrate_a11y">%1$s. Tap to set to vibrate.</string>
    <string name="volume_stream_content_description_mute_a11y">%1$s. Tap to mute.</string>

    <!-- Hint for accessibility. For example: double tap to mute [CHAR_LIMIT=NONE] -->
    <string name="volume_ringer_hint_mute">mute</string>
    <!-- Hint for accessibility. For example: double tap to unmute [CHAR_LIMIT=NONE] -->
    <string name="volume_ringer_hint_unmute">unmute</string>
    <!-- Hint for accessibility. For example: double tap to vibrate [CHAR_LIMIT=NONE] -->
    <string name="volume_ringer_hint_vibrate">vibrate</string>

    <string name="volume_dialog_title">%s volume controls</string>

    <string name="volume_dialog_ringer_guidance_ring">Calls and notifications will ring (<xliff:g id="volume level" example="56">%1$s</xliff:g>)</string>

    <string name="output_title">Media output</string>
    <string name="output_calls_title">Phone call output</string>
    <string name="output_none_found">No devices found</string>
    <string name="output_none_found_service_off">No devices found. Try turning on <xliff:g id="service" example="Bluetooth">%1$s</xliff:g></string>
    <string name="output_service_bt">Bluetooth</string>
    <string name="output_service_wifi">Wi-Fi</string>
    <string name="output_service_bt_wifi">Bluetooth and Wi-Fi</string>

    <!-- Name of special SystemUI debug settings -->
    <string name="system_ui_tuner">System UI Tuner</string>

    <!-- Preference to show/hide embedded battery percentage [CHAR LIMIT=50] -->
    <string name="show_battery_percentage">Show embedded battery percentage</string>

    <!-- Summary for battery percentage preference [CHAR LIMIT=NONE] -->
    <string name="show_battery_percentage_summary">Show battery level percentage inside the status bar icon when not charging</string>

    <!-- Name of quick settings -->
    <string name="quick_settings">Quick Settings</string>

    <!-- Name of status bar -->
    <string name="status_bar">Status bar</string>

    <!-- Name of overview -->
    <string name="overview">Overview</string>

    <!-- Name of System UI demo mode (mode with preset icons for screenshots) -->
    <string name="demo_mode">System UI demo mode</string>

    <!-- Enable demo mode -->
    <string name="enable_demo_mode">Enable demo mode</string>

    <!-- Show demo mode icons -->
    <string name="show_demo_mode">Show demo mode</string>

    <!-- Name of the ethernet status bar icon. -->
    <string name="status_bar_ethernet">Ethernet</string>

    <!-- Name of the alarm status bar icon. -->
    <string name="status_bar_alarm">Alarm</string>

    <!-- Name of the work status bar icon. -->
    <string name="status_bar_work">Work profile</string>

    <!-- Name of the airplane status bar icon. -->
    <string name="status_bar_airplane">Airplane mode</string>

    <!-- Description for adding  a quick settings tile -->
    <string name="add_tile">Add tile</string>

    <!-- Name of a quick settings tile controlled by broadcast -->
    <string name="broadcast_tile">Broadcast Tile</string>

    <!-- For preview release. DO NOT TRANSLATE -->
    <string name="regrettable_lack_of_easter_egg">
        ¯\\_(ツ)_/¯
    </string>

    <!-- Warning text when an alarm might be silenced by Do Not Disturb [CHAR LIMIT=NONE] -->
    <string name="zen_alarm_warning_indef">You won\'t hear your next alarm <xliff:g id="when" example="at 7:00 AM">%1$s</xliff:g> unless you turn this off before then</string>

    <!-- Warning text when an alarm might be silenced by Do Not Disturb due to a time-based condition [CHAR LIMIT=NONE] -->
    <string name="zen_alarm_warning">You won\'t hear your next alarm <xliff:g id="when" example="at 7:00 AM">%1$s</xliff:g></string>

    <!-- Alarm template for near alarms [CHAR LIMIT=25] -->
    <string name="alarm_template">at <xliff:g id="when" example="7:00 AM">%1$s</xliff:g></string>

    <!-- Alarm template for far alarms [CHAR LIMIT=25] -->
    <string name="alarm_template_far">on <xliff:g id="when" example="Fri 7:00 AM">%1$s</xliff:g></string>

    <!-- Accessibility label for Quick Settings detail screens [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_detail">Quick Settings, <xliff:g id="title" example="Wi-Fi">%s</xliff:g>.</string>

    <!-- Accessibility label for hotspot icon [CHAR LIMIT=NONE] -->
    <string name="accessibility_status_bar_hotspot">Hotspot</string>

    <!-- Accessibility label for managed profile icon (not shown on screen) [CHAR LIMIT=NONE] -->
    <string name="accessibility_managed_profile">Work profile</string>

    <!-- Title of warning when entering System UI tuner for first time [CHAR LIMIT=NONE] -->
    <string name="tuner_warning_title">Fun for some but not for all</string>

    <!-- Warning for users entering the System UI tuner for the first time [CHAR LIMIT=NONE]-->
    <string name="tuner_warning">System UI Tuner gives you extra ways to tweak and customize the Android user interface. These experimental features may change, break, or disappear in future releases. Proceed with caution.</string>

    <!-- Warning for users entering the System UI tuner [CHAR LIMIT=NONE]-->
    <string name="tuner_persistent_warning">These experimental features may change, break, or disappear in future releases. Proceed with caution.</string>

    <!-- Generic "got it" acceptance of dialog or cling [CHAR LIMIT=NONE] -->
    <string name="got_it">Got it</string>

    <!-- Toast describing tuner has been enabled [CHAR LIMIT=NONE] -->
    <string name="tuner_toast">Congrats! System UI Tuner has been added to Settings</string>

    <!-- Option to remove the tuner from settings [CHAR LIMIT=NONE] -->
    <string name="remove_from_settings">Remove from Settings</string>

    <!-- Dialog asking if the tuner should really be removed from settings [CHAR LIMIT=NONE]-->
    <string name="remove_from_settings_prompt">Remove System UI Tuner from Settings and stop using all of its features?"</string>

    <!-- Displayed when user launches an app that was uninstalled  [CHAR LIMIT=NONE] -->
    <string name="activity_not_found">Application is not installed on your device</string>

    <!-- Name of setting to show clock seconds [CHAR LIMIT=40] -->
    <string name="clock_seconds">Show clock seconds</string>
    <!-- Description of setting to show clock seconds [CHAR LIMIT=NONE] -->
    <string name="clock_seconds_desc">Show clock seconds in the status bar. May impact battery life.</string>

    <!-- Button that leads to page to rearrange quick settings tiles [CHAR LIMIT=60] -->
    <string name="qs_rearrange">Rearrange Quick Settings</string>
    <!-- Option to show brightness bar in quick settings [CHAR LIMIT=60] -->
    <string name="show_brightness">Show brightness in Quick Settings</string>
    <!-- Option to use new paging layout in quick settings [CHAR LIMIT=60] -->
    <string name="qs_paging" translatable="false">Use the new Quick Settings</string>

    <!-- Category in the System UI Tuner settings, where new/experimental
         settings are -->
    <string name="experimental">Experimental</string>

    <string name="qs_customize" translatable="false">Allow long-press customize in Quick Settings</string>
    <string name="qs_customize_info" translatable="false">Info</string>
    <string name="qs_customize_remove" translatable="false">Remove</string>
    <string name="no_tiles_add" translatable="false">No tiles to add</string>

    <!-- Dialog title asking if Bluetooth should be enabled [CHAR LIMIT=NONE] -->
    <string name="enable_bluetooth_title">Turn on Bluetooth?</string>

    <!-- Dialog message explaining why Bluetooth should be enabled when a packaged keyboard is
         conncted to the device [CHAR LIMIT=NONE] -->
    <string name="enable_bluetooth_message">To connect your keyboard with your tablet, you first have to turn on Bluetooth.</string>

    <!-- Bluetooth enablement ok text [CHAR LIMIT=40] -->
    <string name="enable_bluetooth_confirmation_ok">Turn on</string>

    <!-- [CHAR LIMIT=100] Notification importance option -->
    <string name="show_silently">Show notifications silently</string>
    <!-- [CHAR LIMIT=100] Notification importance option -->
    <string name="block">Block all notifications</string>
    <!-- [CHAR LIMIT=100] Notification importance option -->
    <string name="do_not_silence">Don\'t silence</string>
    <!-- [CHAR LIMIT=100] Notification importance option -->
    <string name="do_not_silence_block">Don\'t silence or block</string>

    <!-- [CHAR LIMIT=NONE] Importance Tuner setting title -->
    <string name="tuner_full_importance_settings">Power notification controls</string>
    <string name="tuner_full_importance_settings_on">On</string>
    <string name="tuner_full_importance_settings_off">Off</string>
    <string name="power_notification_controls_description">With power notification controls, you can set an importance level from 0 to 5 for an app\'s notifications.
        \n\n<b>Level 5</b>
        \n- Show at the top of the notification list
        \n- Allow full screen interruption
        \n- Always peek
        \n\n<b>Level 4</b>
        \n- Prevent full screen interruption
        \n- Always peek
        \n\n<b>Level 3</b>
        \n- Prevent full screen interruption
        \n- Never peek
        \n\n<b>Level 2</b>
        \n- Prevent full screen interruption
        \n- Never peek
        \n- Never make sound and vibration
        \n\n<b>Level 1</b>
        \n- Prevent full screen interruption
        \n- Never peek
        \n- Never make sound or vibrate
        \n- Hide from lock screen and status bar
        \n- Show at the bottom of the notification list
        \n\n<b>Level 0</b>
        \n- Block all notifications from the app
    </string>

    <!-- Notification Inline Controls: Header for apps that are not yet using notification channels. -->
    <string name="notification_header_default_channel">Notifications</string>

    <!-- Notification Inline Controls: Shown when a channel's notifications are currently blocked -->
    <string name="notification_channel_disabled">You won\'t see these notifications anymore</string>

    <!-- Notification inline controls: Shown when a channel's notifications are minimized -->
    <string name="notification_channel_minimized">These notifications will be minimized</string>

    <!-- Notification Inline controls: continue receiving notifications prompt, channel level -->
    <string name="inline_blocking_helper">You usually dismiss these notifications.
    \nKeep showing them?</string>

    <!-- Notification Inline controls: continue receiving notifications prompt, channel level -->
    <string name="inline_keep_showing">Keep showing these notifications?</string>

    <!-- Notification inline controls: block notifications button -->
    <string name="inline_stop_button">Stop notifications</string>

    <!-- Notification inline controls: keep getting notifications button -->
    <string name="inline_keep_button">Keep showing</string>

    <!-- Notification inline controls: minimize notifications button -->
    <string name="inline_minimize_button">Minimize</string>

    <!-- Notification Inline controls: continue receiving notifications prompt, app level -->
    <string name="inline_keep_showing_app">Keep showing notifications from this app?</string>

    <!-- Notification: Control panel: Label that displays when the app's notifications cannot be blocked. -->
    <string name="notification_unblockable_desc">These notifications can\'t be turned off</string>

    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <string name="appops_camera">This app is using the camera.</string>
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <string name="appops_microphone">This app is using the microphone.</string>
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <string name="appops_overlay">This app is displaying over other apps on your screen.</string>
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <string name="appops_camera_mic">This app is using the microphone and camera.</string>
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <string name="appops_camera_overlay">This app is displaying over other apps on your screen and using the camera.</string>
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <string name="appops_mic_overlay">This app is displaying over other apps on your screen and using the microphone.</string>
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <string name="appops_camera_mic_overlay">This app is displaying over other apps on your screen and using the microphone and camera.</string>

    <string name="notification_appops_settings">Settings</string>
    <string name="notification_appops_ok">OK</string>

    <!-- Notification: Control panel: Accessibility description for expanded inline controls view, used
        to control settings about notifications related to the current notification.  -->
    <string name="notification_channel_controls_opened_accessibility">Notification controls for <xliff:g id="app_name" example="YouTube">%1$s</xliff:g> opened</string>
    <!-- Notification: Control panel: Accessibility description for announcing the closing of the
        inline controls view.  -->
    <string name="notification_channel_controls_closed_accessibility">Notification controls for <xliff:g id="app_name" example="YouTube">%1$s</xliff:g> closed</string>
    <!-- Notification: Control panel: Accessibility description for switch that is used to enable
        or disable notifications from this channel -->
    <string name="notification_channel_switch_accessibility">Allow notifications from this channel</string>
    <!-- Notification: Control panel: Label for button that launches notification settings. Used
        when this app has only defined a single channel for notifications. -->
    <string name="notification_more_settings">More settings</string>
    <!-- Notification: Control panel: Label for a link that launches notification settings in the
        app that sent the notification. -->
    <string name="notification_app_settings">Customize</string>
    <!-- Notification: Control panel: Label for button that dismisses control panel. [CHAR LIMIT=NONE] -->
    <string name="notification_done">Done</string>
    <!-- Notification: inline controls: undo block button -->
    <string name="inline_undo">Undo</string>

    <!-- Notification: Menu row: Content description for menu items. [CHAR LIMIT=NONE] -->
    <string name="notification_menu_accessibility"><xliff:g id="app_name" example="YouTube">%1$s</xliff:g> <xliff:g id="menu_description" example="notification controls">%2$s</xliff:g></string>

    <!-- Notification: Menu row: Content description for the gear menu item. [CHAR LIMIT=NONE] -->
    <string name="notification_menu_gear_description">notification controls</string>

    <!-- Notification: Menu row: Content description for the snooze icon. [CHAR LIMIT=NONE] -->
    <string name="notification_menu_snooze_description">notification snooze options</string>

    <!-- Notification: Menu row: Label for the snooze action shown in local context menu. [CHAR LIMIT=NONE] -->
    <string name="notification_menu_snooze_action">Snooze</string>

    <!-- Notification: Snooze panel: Snooze undo button label. [CHAR LIMIT=50]-->
    <string name="snooze_undo">UNDO</string>

    <!-- Notification: Snooze panel: message indicating how long the notification was snoozed for. [CHAR LIMIT=100]-->
    <string name="snoozed_for_time">Snoozed for <xliff:g id="time_amount" example="15 minutes">%1$s</xliff:g></string>

    <!-- Notification:Snooze panel: Snooze options for hours -->
    <plurals name="snoozeHourOptions">
        <item quantity="one">%d hour</item>
        <item quantity="two">%d hours</item>
        <item quantity="few">%d hours</item>
        <item quantity="other">%d hours</item>
    </plurals>

   <!--  Notification: Snooze panel: Snooze options for minutes -->
   <plurals name="snoozeMinuteOptions">
        <item quantity="one">%d minute</item>
        <item quantity="few">%d minutes</item>
        <item quantity="other">%d minutes</item>
    </plurals>

    <!-- Title of the battery settings detail panel [CHAR LIMIT=20] -->
    <string name="battery_panel_title">Battery usage</string>

    <!-- Summary of battery saver not available [CHAR LIMIT=NONE] -->
    <string name="battery_detail_charging_summary">Battery Saver not available during charging</string>

    <!-- Title of switch for battery saver [CHAR LIMIT=NONE] -->
    <string name="battery_detail_switch_title">Battery Saver</string>

    <!-- Summary of switch for battery saver [CHAR LIMIT=NONE] -->
    <string name="battery_detail_switch_summary">Reduces performance and background data</string>

    <!-- Name used for certain Keyboard keys on gamepads, e.g. "Button L1". -->
    <string name="keyboard_key_button_template">Button <xliff:g id="name">%1$s</xliff:g></string>
    <!-- Name used to refer to the "Home" key on the keyboard. -->
    <string name="keyboard_key_home">Home</string>
    <!-- Name used to refer to the "Back" key on the keyboard. -->
    <string name="keyboard_key_back">Back</string>
    <!-- Name used to refer to the "Up" arrow key on the keyboard. -->
    <string name="keyboard_key_dpad_up">Up</string>
    <!-- Name used to refer to the "Down" arrow key on the keyboard. -->
    <string name="keyboard_key_dpad_down">Down</string>
    <!-- Name used to refer to the "Left" arrow key on the keyboard. -->
    <string name="keyboard_key_dpad_left">Left</string>
    <!-- Name used to refer to the "Right" arrow key on the keyboard. -->
    <string name="keyboard_key_dpad_right">Right</string>
    <!-- Name used to refer to the "Center" arrow key on the keyboard. -->
    <string name="keyboard_key_dpad_center">Center</string>
    <!-- Name used to refer to the "Tab" key on the keyboard. -->
    <string name="keyboard_key_tab">Tab</string>
    <!-- Name used to refer to the "Space" key on the keyboard. -->
    <string name="keyboard_key_space">Space</string>
    <!-- Name used to refer to the "Enter" key on the keyboard. -->
    <string name="keyboard_key_enter">Enter</string>
    <!-- Name used to refer to the "Backspace" key on the keyboard. -->
    <string name="keyboard_key_backspace">Backspace</string>
    <!-- Name used to refer to the "Play/Pause" media key on the keyboard. -->
    <string name="keyboard_key_media_play_pause">Play/Pause</string>
    <!-- Name used to refer to the "Stop" media key on the keyboard. -->
    <string name="keyboard_key_media_stop">Stop</string>
    <!-- Name used to refer to the "Next" media key on the keyboard. -->
    <string name="keyboard_key_media_next">Next</string>
    <!-- Name used to refer to the "Previous" media key on the keyboard. -->
    <string name="keyboard_key_media_previous">Previous</string>
    <!-- Name used to refer to the "Rewind" media key on the keyboard. -->
    <string name="keyboard_key_media_rewind">Rewind</string>
    <!-- Name used to refer to the "Fast Forward" media key on the keyboard. -->
    <string name="keyboard_key_media_fast_forward">Fast Forward</string>
    <!-- Name used to refer to the "Page Up" key on the keyboard. -->
    <string name="keyboard_key_page_up">Page Up</string>
    <!-- Name used to refer to the "Page Down" key on the keyboard. -->
    <string name="keyboard_key_page_down">Page Down</string>
    <!-- Name used to refer to the "Delete" key on the keyboard. -->
    <string name="keyboard_key_forward_del">Delete</string>
    <!-- Name used to refer to the "Home" move key on the keyboard. -->
    <string name="keyboard_key_move_home">Home</string>
    <!-- Name used to refer to the "End" move key on the keyboard. -->
    <string name="keyboard_key_move_end">End</string>
    <!-- Name used to refer to the "Insert" key on the keyboard. -->
    <string name="keyboard_key_insert">Insert</string>
    <!-- Name used to refer to the "Num Lock" key on the keyboard. -->
    <string name="keyboard_key_num_lock">Num Lock</string>
    <!-- Name used to refer to keys on the numeric pad of the keyboard, e.g. "Numpad 9". -->
    <string name="keyboard_key_numpad_template">Numpad <xliff:g id="name">%1$s</xliff:g></string>

    <!-- User visible title for the system-wide keyboard shortcuts list. -->
    <string name="keyboard_shortcut_group_system">System</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the home screen. -->
    <string name="keyboard_shortcut_group_system_home">Home</string>
    <!-- User visible title for the the keyboard shortcut that takes the user to the recents screen. -->
    <string name="keyboard_shortcut_group_system_recents">Recents</string>
    <!-- User visible title for the the keyboard shortcut that triggers the back action. -->
    <string name="keyboard_shortcut_group_system_back">Back</string>
    <!-- User visible title for the the keyboard shortcut that triggers the notification shade. -->
    <string name="keyboard_shortcut_group_system_notifications">Notifications</string>
    <!-- User visible title for the the keyboard shortcut that triggers the keyboard shortcuts helper. -->
    <string name="keyboard_shortcut_group_system_shortcuts_helper">Keyboard Shortcuts</string>
    <!-- User visible title for the the keyboard shortcut that switches input methods. -->
    <string name="keyboard_shortcut_group_system_switch_input">Switch input method</string>

    <!-- User visible title for the system-wide applications keyboard shortcuts list. -->
    <string name="keyboard_shortcut_group_applications">Applications</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the assist app. -->
    <string name="keyboard_shortcut_group_applications_assist">Assist</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the browser app. -->
    <string name="keyboard_shortcut_group_applications_browser">Browser</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the contacts app. -->
    <string name="keyboard_shortcut_group_applications_contacts">Contacts</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the email app. -->
    <string name="keyboard_shortcut_group_applications_email">Email</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the SMS messaging app. -->
    <string name="keyboard_shortcut_group_applications_sms">SMS</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the music app. -->
    <string name="keyboard_shortcut_group_applications_music">Music</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the YouTube app. -->
    <string name="keyboard_shortcut_group_applications_youtube">YouTube</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the calendar app. -->
    <string name="keyboard_shortcut_group_applications_calendar">Calendar</string>

    <!-- SysUI Tuner: Option to show full do not disturb panel in volume [CHAR LIMIT=60] -->
    <string name="tuner_full_zen_title">Show with volume controls</string>

    <!-- SysUI Tuner: Label for screen about do not disturb settings [CHAR LIMIT=60] -->
    <string name="volume_and_do_not_disturb">Do not disturb</string>

    <!-- SysUI Tuner: Switch to control whether volume buttons enter/exit do
         not disturb [CHAR LIMIT=60] -->
    <string name="volume_dnd_silent">Volume buttons shortcut</string>

    <!-- SysUI Tuner: Switch to control volume up behavior [CHAR LIMIT=60] -->
    <string name="volume_up_silent">Exit do not disturb on volume up</string>

    <!-- Name of the battery icon in status bar [CHAR LIMIT=30] -->
    <string name="battery">Battery</string>

    <!-- Name of the clock in status bar [CHAR LIMIT=30] -->
    <string name="clock">Clock</string>

    <!-- Name of the headset in status bar [CHAR LIMIT=30] -->
    <string name="headset">Headset</string>

    <!-- Accessibility description for long click on a quick settings tile - this is used in the
         context of the sentence "double tap and hold to _Open settings_" [CHAR LIMIT=NONE] -->
    <string name="accessibility_long_click_tile">Open settings</string>

    <!-- Accessibility description of headphones icon [CHAR LIMIT=NONE] -->
    <string name="accessibility_status_bar_headphones">Headphones connected</string>

    <!-- Accessibility description of headset icon [CHAR LIMIT=NONE] -->
    <string name="accessibility_status_bar_headset">Headset connected</string>

    <!-- Label for quick settings tile for data saver [CHAR LIMIT=30] -->
    <string name="data_saver">Data Saver</string>

    <!-- Accessibility description for data saver being on [CHAR LIMIT=NONE] -->
    <string name="accessibility_data_saver_on">Data Saver is on</string>

    <!-- Accessibility description for data saver being off [CHAR LIMIT=NONE] -->
    <string name="accessibility_data_saver_off">Data Saver is off</string>

    <!-- Label for feature switch [CHAR LIMIT=30] -->
    <string name="switch_bar_on">On</string>

    <!-- Label for feature switch [CHAR LIMIT=30] -->
    <string name="switch_bar_off">Off</string>

    <!-- SysUI Tuner: Button that leads to the navigation bar customization screen [CHAR LIMIT=60] -->
    <string name="nav_bar">Navigation bar</string>

    <!-- SysUI Tuner: Button that controls layout of navigation bar [CHAR LIMIT=60] -->
    <string name="nav_bar_layout">Layout</string>

    <!-- SysUI Tuner: Setting for button type in nav bar [CHAR LIMIT=60] -->
    <string name="left_nav_bar_button_type">Extra left button type</string>

    <!-- SysUI Tuner: Setting for button type in nav bar [CHAR LIMIT=60] -->
    <string name="right_nav_bar_button_type">Extra right button type</string>


    <!-- SysUI Tuner: Added to nav bar option to indicate it is the default [CHAR LIMIT=60] -->
    <string name="nav_bar_default"> (default)</string>

    <!-- SysUI Tuner: Labels for different types of navigation bar buttons [CHAR LIMIT=60] -->
    <string-array name="nav_bar_buttons">
        <item>Clipboard</item>
        <item>Keycode</item>
        <item>Rotate confirm, keyboard switcher</item>
        <item>None</item>
    </string-array>
    <string-array name="nav_bar_button_values" translatable="false">
        <item>clipboard</item>
        <item>key</item>
        <item>menu_ime</item>
        <item>space</item>
    </string-array>

    <!-- SysUI Tuner: Labels for different types of navigation bar layouts [CHAR LIMIT=60] -->
    <string-array name="nav_bar_layouts">
        <item>Normal</item>
        <item>Compact</item>
        <item>Left-leaning</item>
        <item>Right-leaning</item>
    </string-array>

    <string-array name="nav_bar_layouts_values" translatable="false">
        <item>default</item>
        <item>left;back,home,recent;right</item>
        <item>left,back,home,recent,right;space;space</item>
        <item>space;space;left,back,home,recent,right</item>
    </string-array>

    <!-- SysUI Tuner: Name of Combination Menu / Keyboard Switcher button [CHAR LIMIT=30] -->
    <string name="menu_ime">Keyboard switcher</string>
    <!-- SysUI Tuner: Save the current settings [CHAR LIMIT=30] -->
    <string name="save">Save</string>
    <!-- SysUI Tuner: Reset to default settings [CHAR LIMIT=30] -->
    <string name="reset">Reset</string>

    <!-- SysUI Tuner: Adjust button width dialog title [CHAR LIMIT=60] -->
    <string name="adjust_button_width">Adjust button width</string>

    <!-- SysUI Tuner: Nav bar button that holds the clipboard [CHAR LIMIT=30] -->
    <string name="clipboard">Clipboard</string>

    <!-- SysUI Tuner: Accessibility description for custom nav key [CHAR LIMIT=NONE] -->
    <string name="accessibility_key">Custom navigation button</string>

    <!-- SysUI Tuner: Nav bar button that emulates a keycode [CHAR LIMIT=30] -->
    <string name="left_keycode">Left keycode</string>

    <!-- SysUI Tuner: Nav bar button that emulates a keycode [CHAR LIMIT=30] -->
    <string name="right_keycode">Right keycode</string>

    <!-- SysUI Tuner: Settings to change nav bar icon [CHAR LIMIT=30] -->
    <string name="left_icon">Left icon</string>

    <!-- SysUI Tuner: Settings to change nav bar icon [CHAR LIMIT=30] -->
    <string name="right_icon">Right icon</string>

    <!-- Label for area where tiles can be dragged out of [CHAR LIMIT=60] -->
    <string name="drag_to_add_tiles">Hold and drag to add tiles</string>

    <!-- Label for area where tiles can be dragged in to [CHAR LIMIT=60] -->
    <string name="drag_to_remove_tiles">Drag here to remove</string>

    <!-- Label to indicate to users that additional tiles cannot be removed. [CHAR LIMIT=60] -->
    <string name="drag_to_remove_disabled">You need at least 6 tiles</string>

    <!-- Button to edit the tile ordering of quick settings [CHAR LIMIT=60] -->
    <string name="qs_edit">Edit</string>

    <!-- SysUI Tuner: Options for how clock is displayed [CHAR LIMIT=NONE] -->
    <string name="tuner_time">Time</string>

    <!-- SysUI Tuner: Options for how clock is displayed [CHAR LIMIT=NONE] -->
    <string-array name="clock_options">
        <item>Show hours, minutes, and seconds</item>
        <item>Show hours and minutes (default)</item>
        <item>Don\'t show this icon</item>
    </string-array>

    <!-- SysUI Tuner: Options for how battery is displayed [CHAR LIMIT=NONE] -->
    <string-array name="battery_options">
        <item>Always show percentage</item>
        <item>Show percentage when charging (default)</item>
        <item>Don\'t show this icon</item>
    </string-array>

    <!-- SysUI Tuner: Other section -->
    <string name="other">Other</string>

    <!-- Accessibility label for the divider that separates the windows in split-screen mode [CHAR LIMIT=NONE] -->
    <string name="accessibility_divider">Split-screen divider</string>

    <!-- Accessibility action for moving docked stack divider to make the left screen full screen [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_divider_left_full">Left full screen</string>
    <!-- Accessibility action for moving docked stack divider to make the left screen 70% [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_divider_left_70">Left 70%</string>
    <!-- Accessibility action for moving docked stack divider to make the left screen 50% [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_divider_left_50">Left 50%</string>
    <!-- Accessibility action for moving docked stack divider to make the left screen 30% [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_divider_left_30">Left 30%</string>
    <!-- Accessibility action for moving docked stack divider to make the right screen full screen [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_divider_right_full">Right full screen</string>

    <!-- Accessibility action for moving docked stack divider to make the top screen full screen [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_divider_top_full">Top full screen</string>
    <!-- Accessibility action for moving docked stack divider to make the top screen 70% [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_divider_top_70">Top 70%</string>
    <!-- Accessibility action for moving docked stack divider to make the top screen 50% [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_divider_top_50">Top 50%</string>
    <!-- Accessibility action for moving docked stack divider to make the top screen 30% [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_divider_top_30">Top 30%</string>
    <!-- Accessibility action for moving docked stack divider to make the bottom screen full screen [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_divider_bottom_full">Bottom full screen</string>

    <!-- Accessibility description of a QS tile while editing positions [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_tile_label">Position <xliff:g id="position" example="2">%1$d</xliff:g>, <xliff:g id="tile_name" example="Wi-Fi">%2$s</xliff:g>. Double tap to edit.</string>

    <!-- Accessibility description of a QS tile while editing positions [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_add_tile_label"><xliff:g id="tile_name" example="Wi-Fi">%1$s</xliff:g>. Double tap to add.</string>

    <!-- Accessibility description of a place to drop a tile while editing positions [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_position_label">Position <xliff:g id="position" example="2">%1$d</xliff:g>. Double tap to select.</string>

    <!-- Accessibility description of option to move QS tile [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_move_tile">Move <xliff:g id="tile_name" example="Wi-Fi">%1$s</xliff:g></string>

    <!-- Accessibility description of option to remove QS tile [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_remove_tile">Remove <xliff:g id="tile_name" example="Wi-Fi">%1$s</xliff:g></string>

    <!-- Accessibility action when QS tile is added [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_tile_added"><xliff:g id="tile_name" example="Wi-Fi">%1$s</xliff:g> is added to position <xliff:g id="position" example="5">%2$d</xliff:g></string>

    <!-- Accessibility action when QS tile is removed [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_tile_removed"><xliff:g id="tile_name" example="Wi-Fi">%1$s</xliff:g> is removed</string>

    <!-- Accessibility action when QS tile is moved [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_tile_moved"><xliff:g id="tile_name" example="Wi-Fi">%1$s</xliff:g> moved to position <xliff:g id="position" example="5">%2$d</xliff:g></string>

    <!-- Accessibility label for window when QS editing is happening [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_quick_settings_edit">Quick settings editor.</string>

    <!-- Accessibility label for the notification icons in the collapsed status bar. Not shown on screen [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_notification_icon"><xliff:g name="app_name" example="Gmail">%1$s</xliff:g> notification: <xliff:g name="notification_text" example="5 new messages">%2$s</xliff:g></string>

    <!-- Label for button that reports a touch that was wrongly rejected by the lockscreen. For debugging only. [CHAR LIMIT=NONE] -->
    <string name="report_rejected_touch" translatable="false">Report rejected touch</string>

    <!-- Multi-Window strings -->
    <!-- Text that gets shown on top of current activity to inform the user that the system force-resized the current activity to be displayed in split-screen and that things might crash/not work properly [CHAR LIMIT=NONE] -->
    <string name="dock_forced_resizable">App may not work with split-screen.</string>
    <!-- Warning message when we try to dock a non-resizeable task and launch it in fullscreen instead. -->
    <string name="dock_non_resizeble_failed_to_dock_text">App does not support split-screen.</string>
    <!-- Text that gets shown on top of current activity to inform the user that the system force-resized the current activity to be displayed on a secondary display and that things might crash/not work properly [CHAR LIMIT=NONE] -->
    <string name="forced_resizable_secondary_display">App may not work on a secondary display.</string>
    <!-- Warning message when we try to launch a non-resizeable activity on a secondary display and launch it on the primary instead. -->
    <string name="activity_launch_on_secondary_display_failed_text">App does not support launch on secondary displays.</string>

    <!-- accessibility label for button to open settings [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_settings">Open settings.</string>

    <!-- accessibility label for button to expand quick settings [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_expand">Open quick settings.</string>

    <!-- accessibility label for button to collapse quick settings [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_collapse">Close quick settings.</string>

    <!-- accessibility label for alarm icon [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_alarm_set">Alarm set.</string>

    <!-- accessibility label for button to select user [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_user">Signed in as <xliff:g name="user" example="John">%s</xliff:g></string>

    <!-- Content description for no internet connection [CHAR LIMIT=NONE] -->
    <string name="data_connection_no_internet">No internet</string>

    <!-- accessibility label for quick settings items that open a details page [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_open_details">Open details.</string>

    <!-- accessibility label for quick settings items that open a details page [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_open_settings">Open <xliff:g name="page" example="Bluetooth">%s</xliff:g> settings.</string>

    <!-- accessibility label for button to edit quick settings [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_edit">Edit order of settings.</string>

    <!-- accessibility label for paging indicator in quick settings [CHAR LIMITi=NONE] -->
    <string name="accessibility_quick_settings_page">Page <xliff:g name="current_page" example="1">%1$d</xliff:g> of <xliff:g name="num_pages" example="2">%2$d</xliff:g></string>

    <!-- Plugin control section of the tuner. Non-translatable since it should
         not appear on production builds ever. -->
    <string name="plugins" translatable="false">Plugins</string>

    <!-- Ambient display section of the tuner. Non-translatable since it should
        not appear on production builds ever. -->
    <string name="tuner_doze" translatable="false">Ambient Display</string>

    <!-- Ambient display always-on of the tuner. Non-translatable since it should
        not appear on production builds ever. -->
    <string name="tuner_doze_always_on" translatable="false">Always on</string>

    <!-- SysUI Tuner: Section to customize lockscreen shortcuts [CHAR LIMIT=60] -->
    <string name="tuner_lock_screen">Lock screen</string>

    <!-- Making the PIP fullscreen [CHAR LIMIT=25] -->
    <string name="pip_phone_expand">Expand</string>

    <!-- Label for PIP action to Minimize the PIP [CHAR LIMIT=25] -->
    <string name="pip_phone_minimize">Minimize</string>

    <!-- Label for PIP close button [CHAR LIMIT=NONE]-->
    <string name="pip_phone_close">Close</string>

    <!-- Label for PIP settings button [CHAR LIMIT=NONE]-->
    <string name="pip_phone_settings">Settings</string>

    <!-- Label for PIP the drag to dismiss hint [CHAR LIMIT=NONE]-->
    <string name="pip_phone_dismiss_hint">Drag down to dismiss</string>

    <!-- Title of menu shown over picture-in-picture. Used for accessibility. -->
    <string name="pip_menu_title">Menu</string>

    <!-- PiP BTW notification title. [CHAR LIMIT=50] -->
    <string name="pip_notification_title"><xliff:g id="name" example="Google Maps">%s</xliff:g> is in picture-in-picture</string>

    <!-- PiP BTW notification description. [CHAR LIMIT=NONE] -->
    <string name="pip_notification_message">If you don\'t want <xliff:g id="name" example="Google Maps">%s</xliff:g> to use this feature, tap to open settings and turn it off.</string>

    <!-- Button to play the current media on picture-in-picture (PIP) [CHAR LIMIT=30] -->
    <string name="pip_play">Play</string>

    <!-- Button to pause the current media on picture-in-picture (PIP) [CHAR LIMIT=30] -->
    <string name="pip_pause">Pause</string>

    <!-- Button to skip to the next media on picture-in-picture (PIP) [CHAR LIMIT=30] -->
    <string name="pip_skip_to_next">Skip to next</string>

    <!-- Button to skip to the prev media on picture-in-picture (PIP) [CHAR LIMIT=30] -->
    <string name="pip_skip_to_prev">Skip to previous</string>

    <!-- Tuner string -->
    <string name="change_theme_reboot" translatable="false">Changing the theme requires a restart.</string>
    <!-- Tuner string -->
    <string name="theme" translatable="false">Theme</string>
    <!-- Tuner string -->
    <string name="default_theme" translatable="false">Default</string>

    <!-- Title for notification & dialog that the user's phone last shut down because it got too hot. [CHAR LIMIT=40] -->
    <string name="thermal_shutdown_title">Phone turned off due to heat</string>
    <!-- Message body for notification that user's phone last shut down because it got too hot. [CHAR LIMIT=100] -->
    <string name="thermal_shutdown_message">Your phone is now running normally</string>
    <!-- Text body for dialog alerting user that their phone last shut down because it got too hot. [CHAR LIMIT=450] -->
    <string name="thermal_shutdown_dialog_message">Your phone was too hot, so it turned off to cool down. Your phone is now running normally.\n\nYour phone may get too hot if you:\n\t&#8226; Use resource-intensive apps (such as gaming, video, or navigation apps)\n\t&#8226; Download or upload large files\n\t&#8226; Use your phone in high temperatures</string>

    <!-- Title for notification (and dialog) that user's phone has reached a certain temperature and may start to slow down in order to cool down. [CHAR LIMIT=30] -->
    <string name="high_temp_title">Phone is getting warm</string>
    <!-- Message body for notification that user's phone has reached a certain temperature and may start to slow down in order to cool down. [CHAR LIMIT=100] -->
    <string name="high_temp_notif_message">Some features limited while phone cools down</string>
    <!-- Text body for dialog alerting user that their phone has reached a certain temperature and may start to slow down in order to cool down. [CHAR LIMIT=300] -->
    <string name="high_temp_dialog_message">Your phone will automatically try to cool down. You can still use your phone, but it may run slower.\n\nOnce your phone has cooled down, it will run normally.</string>

    <!-- SysUI Tuner: Button to select lock screen shortcut [CHAR LIMIT=60] -->
    <string name="lockscreen_shortcut_left">Left shortcut</string>

    <!-- SysUI Tuner: Button to select lock screen shortcut [CHAR LIMIT=60] -->
    <string name="lockscreen_shortcut_right">Right shortcut</string>

    <!-- SysUI Tuner: Switch to control if device gets unlocked by left shortcut [CHAR LIMIT=60] -->
    <string name="lockscreen_unlock_left">Left shortcut also unlocks</string>

    <!-- SysUI Tuner: Switch to control if device gets unlocked by right shortcut [CHAR LIMIT=60] -->
    <string name="lockscreen_unlock_right">Right shortcut also unlocks</string>

    <!-- SysUI Tuner: Summary of no shortcut being selected [CHAR LIMIT=60] -->
    <string name="lockscreen_none">None</string>

    <!-- SysUI Tuner: Format string for describing launching an app [CHAR LIMIT=60] -->
    <string name="tuner_launch_app">Launch <xliff:g id="app" example="Settings">%1$s</xliff:g></string>

    <!-- SysUI Tuner: Label for section of other apps that can be launched [CHAR LIMIT=60] -->
    <string name="tuner_other_apps">Other apps</string>

    <!-- SysUI Tuner: Label for icon shaped like a circle [CHAR LIMIT=60] -->
    <string name="tuner_circle">Circle</string>

    <!-- SysUI Tuner: Label for icon shaped like a plus [CHAR LIMIT=60] -->
    <string name="tuner_plus">Plus</string>

    <!-- SysUI Tuner: Label for icon shaped like a minus [CHAR LIMIT=60] -->
    <string name="tuner_minus">Minus</string>

    <!-- SysUI Tuner: Label for icon shaped like a left [CHAR LIMIT=60] -->
    <string name="tuner_left">Left</string>

    <!-- SysUI Tuner: Label for icon shaped like a right [CHAR LIMIT=60] -->
    <string name="tuner_right">Right</string>

    <!-- SysUI Tuner: Label for icon shaped like a menu [CHAR LIMIT=60] -->
    <string name="tuner_menu">Menu</string>

    <!-- SysUI Tuner: App subheading for shortcut selection [CHAR LIMIT=60] -->
    <string name="tuner_app"><xliff:g id="app">%1$s</xliff:g> app</string>

    <!-- Title for the notification channel containing important alerts. [CHAR LIMIT=NONE] -->
    <string name="notification_channel_alerts">Alerts</string>
    <!-- Title for the notification channel for battery warnings (i.e. < 15%). [CHAR LIMIT=NONE] -->
    <string name="notification_channel_battery">Battery</string>
    <!-- Title for the notification channel dedicated to screenshot progress. [CHAR LIMIT=NONE] -->
    <string name="notification_channel_screenshot">Screenshots</string>
    <!-- Title for the notification channel for miscellaneous notices. [CHAR LIMIT=NONE] -->
    <string name="notification_channel_general">General Messages</string>
    <!-- Title for the notification channel for problems with storage (i.e. low disk). [CHAR LIMIT=NONE] -->
    <string name="notification_channel_storage">Storage</string>
    <!-- Title for the notification channel for hints and suggestions. [CHAR LIMIT=NONE] -->
    <string name="notification_channel_hints">Hints</string>

    <!-- App label of the instant apps notification [CHAR LIMIT=60] -->
    <string name="instant_apps">Instant Apps</string>

    <!-- Message of the instant apps notification indicating they don't need install [CHAR LIMIT=NONE] -->
    <string name="instant_apps_message">Instant apps don\'t require installation.</string>

    <!-- Action label for launching app info on the specified app [CHAR LIMIT=20] -->
    <string name="app_info">App info</string>

    <!-- Action label for switching to a browser for an instant app [CHAR LIMIT=20] -->
    <string name="go_to_web">Go to browser</string>

    <!-- Quick settings tile for toggling mobile data [CHAR LIMIT=20] -->
    <string name="mobile_data">Mobile data</string>

    <!-- Quick settings tile secondary label format combining roaming with the mobile data type. [CHAR LIMIT=NONE] -->
    <string name="mobile_data_text_format"><xliff:g name="roaming_status" example="Roaming">%s</xliff:g> \u2014 <xliff:g name="mobile_data_type" example="LTE">%s</xliff:g></string>

    <!-- Label for when wifi is off in QS detail panel [CHAR LIMIT=NONE] -->
    <string name="wifi_is_off">Wi-Fi is off</string>

    <!-- Label for when bluetooth is off in QS detail panel [CHAR LIMIT=NONE] -->
    <string name="bt_is_off">Bluetooth is off</string>

    <!-- Label for when Do not disturb is off in QS detail panel [CHAR LIMIT=NONE] -->
    <string name="dnd_is_off">Do Not Disturb is off</string>

    <!-- Prompt for when Do not disturb is on from automatic rule in QS [CHAR LIMIT=NONE] -->
    <string name="qs_dnd_prompt_auto_rule">Do Not Disturb was turned on by an automatic rule (<xliff:g name="rule">%s</xliff:g>).</string>

    <!-- Prompt for when Do not disturb is on from app in QS [CHAR LIMIT=NONE] -->
    <string name="qs_dnd_prompt_app">Do Not Disturb was turned on by an app (<xliff:g name="app">%s</xliff:g>).</string>

    <!-- Prompt for when Do not disturb is on from automatic rule or app in QS [CHAR LIMIT=NONE] -->
    <string name="qs_dnd_prompt_auto_rule_app">Do Not Disturb was turned on by an automatic rule or app.</string>

    <!-- Description of Do Not Disturb option in QS that ends at the specified time[CHAR LIMIT=20] -->
    <string name="qs_dnd_until">Until <xliff:g name="time">%s</xliff:g></string>

    <!-- Do Not Disturb button to keep the current settings [CHAR LIMIT=20] -->
    <string name="qs_dnd_keep">Keep</string>

    <!-- Do Not Disturb button to change the current settings [CHAR LIMIT=20] -->
    <string name="qs_dnd_replace">Replace</string>

    <!-- Title of the "running foreground services" dialog. [CHAR LIMIT=NONE] -->
    <string name="running_foreground_services_title">Apps running in background</string>

    <!-- Descriptive text of an item in the "running foreground services" dialog, telling the
        user what will happen when they tap on that item (which is an application that has
        been identified for them as running). [CHAR LIMIT=NONE] -->
    <string name="running_foreground_services_msg">Tap for details on battery and data usage</string>

    <!-- Title of the dialog to turn off data usage [CHAR LIMIT=NONE] -->
    <string name="mobile_data_disable_title">Turn off mobile data?</string>

    <!-- Message body of the dialog to turn off data usage [CHAR LIMIT=NONE] -->
    <string name="mobile_data_disable_message">You won\’t have access to data or the internet through <xliff:g id="carrier" example="T-Mobile">%s</xliff:g>. Internet will only be available via Wi-Fi.</string>

    <!-- Text used to refer to the user's current carrier in mobile_data_disable_message if the users's mobile network carrier name is not available [CHAR LIMIT=NONE] -->
    <string name="mobile_data_disable_message_default_carrier">your carrier</string>

    <!-- Warning shown when user input has been blocked due to another app overlaying screen
     content. Since we don't know what the app is showing on top of the input target, we
     can't verify user consent. [CHAR LIMIT=NONE] -->
    <string name="touch_filtered_warning">Because an app is obscuring a permission request, Settings
        can’t verify your response.</string>

    <!-- Title of prompt requesting access to display slices [CHAR LIMIT=NONE] -->
    <string name="slice_permission_title">Allow <xliff:g id="app" example="Example App">%1$s</xliff:g> to show <xliff:g id="app_2" example="Other Example App">%2$s</xliff:g> slices?</string>

    <!-- Description of what kind of access is given to a slice host [CHAR LIMIT=NONE] -->
    <string name="slice_permission_text_1"> - It can read information from <xliff:g id="app" example="Example App">%1$s</xliff:g></string>
    <!-- Description of what kind of access is given to a slice host [CHAR LIMIT=NONE] -->
    <string name="slice_permission_text_2"> - It can take actions inside <xliff:g id="app" example="Example App">%1$s</xliff:g></string>

    <!-- Text on checkbox allowing the app to show slices from all apps [CHAR LIMIT=NONE] -->
    <string name="slice_permission_checkbox">Allow <xliff:g id="app" example="Example App">%1$s</xliff:g> to show slices from any app</string>

    <!-- Option to grant the slice permission request on the screen [CHAR LIMIT=15] -->
    <string name="slice_permission_allow">Allow</string>

    <!-- Option to grant the slice permission request on the screen [CHAR LIMIT=15] -->
    <string name="slice_permission_deny">Deny</string>

    <!-- List of packages for which we don't want to show recents onboarding, add into overlay as needed. -->
    <string-array name="recents_onboarding_blacklisted_packages" translatable="false">
    </string-array>

    <!-- The title of the notification to suggest enabling automatic battery saver.  [CHAR LIMIT=NONE]-->
    <string name="auto_saver_title">Tap to schedule Battery Saver</string>

    <!-- The content of the notification to suggest enabling automatic battery saver.  [CHAR LIMIT=NONE]-->
    <string name="auto_saver_text">Turn on automatically when battery is at <xliff:g id="percentage">%d</xliff:g>%%</string>

    <!-- An action on the notification to suggest enabling automatic battery saver: Do not turn on automatic battery saver.  [CHAR LIMIT=NONE]-->
    <string name="no_auto_saver_action">No thanks</string>

    <!-- The title of the dialog that tells that scheduled (i.e. automatic) battery saver has been turned on. [CHAR LIMIT=NONE]-->
    <string name="auto_saver_enabled_title">Battery Saver schedule turned on</string>

    <!-- The content of the dialog that tells that scheduled (i.e. automatic) battery saver has been turned on. [CHAR LIMIT=NONE]-->
    <string name="auto_saver_enabled_text">Battery Saver will turn on automatically once battery goes below <xliff:g id="percentage">%d</xliff:g>%%.</string>

    <!-- An action on the dialog that tells that scheduled (i.e. automatic) battery saver: open the battery saver setting.  [CHAR LIMIT=NONE]-->
    <string name="open_saver_setting_action">Settings</string>

    <!-- An action on the dialog that tells that scheduled (i.e. automatic) battery saver: user acknowledges and closes the dialog.  [CHAR LIMIT=NONE]-->
    <string name="auto_saver_okay_action">Got it</string>

    <!-- URl of the webpage that explains battery saver. -->
    <string name="help_uri_battery_saver_learn_more_link_target" translatable="false"></string>

    <!-- Name for a quick settings tile, used only by platform developers, to extract the SystemUI process memory and send it to another
         app for debugging. Will not be seen by users. [CHAR LIMIT=20] -->
    <string name="heap_dump_tile_name">Dump SysUI Heap</string>


    <string name="accessibility_quick_settings_audio_record_changed_on">唤醒已打开</string>
    <string name="accessibility_quick_settings_audio_record_changed_off">唤醒已禁用</string>
    <string name="accessibility_quick_settings_sleep_mode_on">睡眠模式已打开</string>
    <string name="accessibility_quick_settings_sleep_mode_off">睡眠模式已关闭</string>
    <string name="accessibility_quick_settings_child_mode_off">退出请在小爱音箱APP-我的-儿童模式中操作</string>
    <string name="accessibility_quick_settings_ble_on">蓝牙已打开</string>
    <string name="accessibility_quick_settings_ble_off">蓝牙已关闭</string>
    <string name="accessibility_quick_settings_camera_on">摄像头已打开</string>
    <string name="accessibility_quick_settings_camera_off">摄像头已禁用</string>

    <string name="qs_home_title">首页</string>
    <string name="qs_camera_title">摄像头</string>
    <string name="qs_child_mode_title">儿童模式</string>
    <string name="qs_night_mode_title">夜间模式</string>
    <string name="qs_bluetooth_title">蓝牙</string>
    <string name="qs_setting_title">设置</string>
    <string name="qs_record_audio_title">唤醒</string>
    <string name="qs_player_title_default">音乐</string>
    <string name="qs_elder_mode_title">长辈模式</string>
    <string name="qs_auto_light_mode_tile">自动亮度</string>
    <string name="qs_ui_mode_tile">深色模式</string>
    <string name="default_wifi_name">Wi-Fi</string>
    <string name="default_wifi_state">未连接</string>
    <string name="wifi_state_close">已关闭</string>
    <string name="wifi_state_connected">已连接</string>
    <string name="qs_bluetooth_mode_tile">蓝牙</string>
    <string name="qs_low_power_mode_tile">省电模式</string>

    <string name="global_action_restart">重启</string>

    <string name="notification_container_title">暂时没有新通知</string>

    <string name="font_sans_serif_medium" translatable="false">sans-serif-medium</string>

    <string name="camera_covered_toast_hint">请滑动背面挡板开启摄像头</string>
    <string name="top_camera_covered_toast_hint">请滑动顶部挡板开启摄像头</string>
    <string name="camera_software_enable_hint">摄像头已开启</string>
    <string name="camera_software_disable_hint">摄像头已关闭</string>
    <string name="microphone_mute_hint">麦克风已关闭</string>
    <string name="microphone_normal_hint">麦克风已开启</string>
    <string name="auto_light_mode_on">自动亮度已打开</string>
    <string name="auto_light_mode_off">自动亮度已关闭</string>
    <string name="blue_tooth_on">蓝牙已打开，15分钟内无设备连接将自动关闭</string>
    <string name="blue_tooth_off">蓝牙已关闭</string>
    <string name="theme_mode_dark_on">已切换到深色模式</string>
    <string name="theme_mode_dark_off">已切换到浅色模式</string>
    <string name="low_power_mode_on">已启用省电模式，蓝牙Mesh网关等功能将被关闭</string>
    <string name="low_power_mode_off">已退出省电模式</string>
    <string name="low_power_mode_unavailable">请先关闭"永不"息屏模式，再打开省电模式</string>
    <string name="low_power_mode_open_bluetooth_mesh">已退出省电模式，蓝牙Mesh网关等功能已启用</string>
    <string name="yesterday">昨天</string>
    <!-- XIAOMI END -->

</resources>
