<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright (c) 2016, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->
<resources>
    <!-- Name of Guest Profile. [CHAR LIMIT=30] -->
    <string name="car_guest">Guest</string>
    <!-- Name of Add User Profile. [CHAR LIMIT=30] -->
    <string name="car_add_user">Add User</string>
    <!-- Default name of the new user created. [CHAR LIMIT=30] -->
    <string name="car_new_user">New User</string>
    <!-- Message to inform user that creation of new user requires that user to set up their space. [CHAR LIMIT=100] -->
    <string name="user_add_user_message_setup">When you add a new user, that person needs to set up their space.</string>
    <!-- Message to inform user that the newly created user will have permissions to update apps for all other users. [CHAR LIMIT=100] -->
    <string name="user_add_user_message_update">Any user can update apps for all other users.</string>
</resources>
