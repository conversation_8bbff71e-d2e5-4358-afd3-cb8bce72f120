<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2017, The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<com.android.systemui.statusbar.NotificationInfo
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/notification_guts"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical"
    android:background="@color/notification_guts_bg_color"
    android:theme="@*android:style/Theme.DeviceDefault.Light">

    <!-- Package Info -->
    <RelativeLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@*android:dimen/notification_content_margin_start"
        android:clipChildren="false"
        android:clipToPadding="false">
        <ImageView
            android:id="@+id/pkgicon"
            android:layout_width="@dimen/notification_guts_header_height"
            android:layout_height="@dimen/notification_guts_header_height"
            android:layout_centerVertical="true"
            android:layout_marginEnd="3dp" />
        <TextView
            android:id="@+id/pkgname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@*android:style/TextAppearance.Material.Notification.Info"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="2dp"
            android:singleLine="true"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/pkgicon" />
        <TextView
            android:id="@+id/pkg_group_divider"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@*android:style/TextAppearance.Material.Notification.Info"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="2dp"
            android:text="@*android:string/notification_header_divider_symbol"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/pkgname" />
        <TextView
            android:id="@+id/group_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@*android:style/TextAppearance.Material.Notification.Info"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/pkg_group_divider" />
        <!-- 24 dp icon with 16 dp padding all around to mirror notification content margins -->
        <ImageButton
            android:id="@+id/info"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@drawable/ripple_drawable"
            android:contentDescription="@string/notification_more_settings"
            android:padding="16dp"
            android:src="@drawable/ic_info"
            android:tint="?android:attr/colorAccent" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/prompt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/notification_guts_button_spacing"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="vertical">

        <!-- Channel Info Block -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@*android:dimen/notification_content_margin_start"
            android:layout_marginEnd="@*android:dimen/notification_content_margin_start"
            android:orientation="vertical">
            <!-- Channel Name -->
            <TextView
                android:id="@+id/channel_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                style="@android:style/TextAppearance.Material.Notification.Title" />
            <!-- Question prompt -->
            <TextView
                android:id="@+id/block_prompt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@android:style/TextAppearance.Material.Notification" />
        </LinearLayout>

        <!-- Settings and Done buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/notification_guts_button_spacing"
            android:layout_marginStart="@dimen/notification_guts_button_side_margin"
            android:layout_marginEnd="@dimen/notification_guts_button_side_margin"
            android:gravity="end"
            android:orientation="horizontal">

            <!-- Optional link to app. Only appears if the channel is not disabled and the app
            asked for it -->
            <TextView
                android:id="@+id/app_settings"
                android:text="@string/notification_app_settings"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:ellipsize="end"
                android:maxLines="1"
                style="@style/TextAppearance.NotificationInfo.Button"/>
            <TextView
                android:id="@+id/block"
                android:text="@string/inline_stop_button"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/notification_guts_button_horizontal_spacing"
                style="@style/TextAppearance.NotificationInfo.Button"/>
            <TextView
                android:id="@+id/minimize"
                android:text="@string/inline_minimize_button"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/notification_guts_button_horizontal_spacing"
                style="@style/TextAppearance.NotificationInfo.Button" />
            <TextView
                android:id="@+id/keep"
                android:text="@string/inline_keep_button"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/notification_guts_button_horizontal_spacing"
                style="@style/TextAppearance.NotificationInfo.Button"/>
        </LinearLayout>
    </LinearLayout>
    <RelativeLayout
        android:id="@+id/confirmation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/notification_guts_button_spacing"
        android:layout_marginTop="@dimen/notification_guts_button_spacing"
        android:layout_marginStart="@dimen/notification_guts_button_side_margin"
        android:layout_marginEnd="@dimen/notification_guts_button_side_margin"
        android:visibility="gone"
        android:orientation="horizontal" >
        <TextView
            android:id="@+id/confirmation_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/notification_channel_disabled"
            style="@style/TextAppearance.NotificationInfo.Confirmation"/>
        <TextView
            android:id="@+id/undo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="@string/inline_undo"
            style="@style/TextAppearance.NotificationInfo.Button"/>
    </RelativeLayout>
</com.android.systemui.statusbar.NotificationInfo>
