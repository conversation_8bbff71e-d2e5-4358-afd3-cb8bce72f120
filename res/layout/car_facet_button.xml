<?xml version="1.0" encoding="utf-8"?>
<!--
**
** Copyright 2017, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/car_facet_button"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:animateLayoutChanges="true"
        android:orientation="vertical">

        <com.android.keyguard.AlphaOptimizedImageButton
            android:id="@+id/car_nav_button_icon"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:animateLayoutChanges="true"
            android:background="@android:color/transparent"
            android:scaleType="fitCenter">
        </com.android.keyguard.AlphaOptimizedImageButton>

        <com.android.keyguard.AlphaOptimizedImageButton
            android:id="@+id/car_nav_button_more_icon"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:animateLayoutChanges="true"
            android:src="@drawable/car_ic_arrow"
            android:background="@android:color/transparent"
            android:scaleType="fitCenter">
        </com.android.keyguard.AlphaOptimizedImageButton>

    </LinearLayout>
</merge>
