<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<!-- Extends LinearLayout -->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/text2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="8dp"
        android:paddingStart="8dp"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:text="@string/slice_permission_text_1" />

    <TextView
        android:id="@+id/text1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="8dp"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:paddingBottom="16dp"
        android:text="@string/slice_permission_text_2" />

    <CheckBox
        android:id="@+id/slice_permission_checkbox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/slice_permission_checkbox" />

</LinearLayout>
