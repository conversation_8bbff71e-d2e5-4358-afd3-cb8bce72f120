<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/car_user_switcher_background_color"
        android:visibility="gone">

    <LinearLayout
        android:id="@+id/container"
        android:background="@color/car_user_switcher_background_color"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include layout="@layout/car_status_bar_header"
            android:theme="@android:style/Theme"
            android:layout_alignParentTop="true"/>

        <com.android.systemui.statusbar.car.UserGridRecyclerView
            android:id="@+id/user_grid"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/car_user_switcher_margin_top"
            app:verticallyCenterListContent="true"
            app:dayNightStyle="always_light"
            app:showPagedListViewDivider="false"
            app:gutter="both"
            app:itemSpacing="@dimen/car_user_switcher_vertical_spacing_between_users"/>

    </LinearLayout>
</FrameLayout>
