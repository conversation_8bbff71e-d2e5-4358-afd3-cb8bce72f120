<?xml version="1.0" encoding="utf-8"?>
<!--
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- Extends FrameLayout -->
<com.android.systemui.assist.AssistOrbContainer
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.android.systemui.statusbar.AlphaOptimizedView
        android:layout_width="match_parent"
        android:layout_height="@dimen/assist_orb_scrim_height"
        android:layout_gravity="bottom"
        android:id="@+id/assist_orb_scrim"
        android:background="@drawable/assist_orb_scrim"/>

    <com.android.systemui.assist.AssistOrbView
        android:id="@+id/assist_orb"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/search_logo"/>
    </com.android.systemui.assist.AssistOrbView>

    <com.android.systemui.statusbar.AlphaOptimizedView
        android:id="@+id/assist_orb_navbar_scrim"
        android:layout_height="@dimen/assist_orb_navbar_scrim_height"
        android:layout_width="match_parent"
        android:layout_gravity="bottom"
        android:elevation="50dp"
        android:outlineProvider="none"
        android:background="@drawable/assist_orb_navbar_scrim"/>

</com.android.systemui.assist.AssistOrbContainer>