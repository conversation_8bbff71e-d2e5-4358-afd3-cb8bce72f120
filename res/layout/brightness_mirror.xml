<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/brightness_mirror"
    android:layout_width="@dimen/qs_panel_width"
    android:layout_height="@dimen/brightness_mirror_height"
    android:layout_gravity="@integer/notification_panel_layout_gravity"
    android:visibility="invisible">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/notification_side_paddings"
        android:layout_marginRight="@dimen/notification_side_paddings"
        android:background="@drawable/brightness_mirror_background">
        <include layout="@layout/quick_settings_brightness_dialog" />
    </FrameLayout>
</FrameLayout>
