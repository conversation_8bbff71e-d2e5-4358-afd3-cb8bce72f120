<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright (c) 2014, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_height="@dimen/screen_pinning_request_width"
    android:layout_width="wrap_content"
    android:gravity="left|center_vertical"
    android:orientation="horizontal"
    android:theme="@android:style/Theme.DeviceDefault.Light">

    <include
        android:layout_width="wrap_content"
        android:layout_height="@dimen/screen_pinning_request_width"
        layout="@layout/screen_pinning_request_buttons_sea" />

    <include
        android:layout_width="360dp"
        android:layout_height="@dimen/screen_pinning_request_width"
        layout="@layout/screen_pinning_request_text_area" />

</LinearLayout>
