<!--
  ~ Copyright (C) 2014 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<!-- Extends Framelayout -->
<com.android.systemui.statusbar.FooterView
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="4dp"
        android:paddingEnd="4dp"
        android:visibility="gone">
    <com.android.systemui.statusbar.AlphaOptimizedFrameLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" >
        <com.android.systemui.statusbar.FooterViewButton
            style="@android:style/Widget.Material.Button.Borderless"
            android:id="@+id/manage_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:focusable="true"
            android:text="@string/manage_notifications_text"
            android:textColor="?attr/wallpaperTextColor"
            android:textAllCaps="false"/>
        <com.android.systemui.statusbar.FooterViewButton
            style="@android:style/Widget.Material.Button.Borderless"
            android:id="@+id/dismiss_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:focusable="true"
            android:contentDescription="@string/accessibility_clear_all"
            android:text="@string/clear_all_notifications_text"
            android:textColor="?attr/wallpaperTextColor"/>
    </com.android.systemui.statusbar.AlphaOptimizedFrameLayout>
</com.android.systemui.statusbar.FooterView>
