<!--
  ~ Copyright (C) 2014 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<!-- Extends Framelayout -->
<com.android.systemui.statusbar.EmptyShadeView
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        >
    <TextView
            android:id="@+id/no_notifications"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="64dp"
            android:paddingTop="28dp"
            android:gravity="top|center_horizontal"
            android:textColor="?attr/wallpaperTextColor"
            android:textSize="16sp"
            android:text="@string/empty_shade_text"/>
</com.android.systemui.statusbar.EmptyShadeView>
