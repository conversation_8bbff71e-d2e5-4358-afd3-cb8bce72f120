<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/notification_background_color">

    <LinearLayout
        android:id="@+id/notification_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/mi_lock_screen_notification_center_title_margin_top">

        <ImageView
            android:id="@+id/notification_back"
            android:layout_width="@dimen/mi_lock_screen_notification_center_title_icon_size"
            android:layout_height="@dimen/mi_lock_screen_notification_center_title_icon_size"
            android:layout_marginLeft="@dimen/mi_lock_screen_notification_center_title_icon_margin"
            android:scaleType="center"
            android:src="@drawable/ic_mi_notification_center_back" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="通知中心"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/mi_lock_screen_notification_center_title_text_size" />

        <ImageView
            android:id="@+id/notification_settings"
            android:layout_width="@dimen/mi_lock_screen_notification_center_title_icon_size"
            android:layout_height="@dimen/mi_lock_screen_notification_center_title_icon_size"
            android:layout_marginRight="@dimen/mi_lock_screen_notification_center_title_icon_margin"
            android:scaleType="center"
            android:src="@drawable/ic_mi_notification_center_settings" />
    </LinearLayout>

    <com.android.systemui.qs.mi.notification.MiNotificationContainer
        android:id="@+id/notification_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/mi_lock_screen_notification_center_container_margin_top" />

    <ImageView
        android:id="@+id/dismiss_view"
        android:layout_width="@dimen/status_bar_fold_clear_icon_size"
        android:layout_height="@dimen/status_bar_fold_clear_icon_size"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="@dimen/mi_lock_screen_notification_center_dismiss_view_margin_right"
        android:layout_marginBottom="@dimen/mi_lock_screen_notification_center_dismiss_view_margin_bottom"
        android:background="@drawable/clear_mi_notification"
        android:focusable="true" />

    <ImageView
        android:id="@+id/notification_none"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/mi_lock_screen_notification_center_none_guide_margin_bottom"
        android:src="@drawable/ic_mi_notification_center_none" />

</RelativeLayout>