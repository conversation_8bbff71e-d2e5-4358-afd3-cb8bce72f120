<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/pip_dismiss_gradient_height"
    android:alpha="0">

    <TextView
        android:id="@+id/pip_dismiss_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:text="@string/pip_phone_dismiss_hint"
        android:textColor="#FFFFFFFF"
        android:textSize="14sp"
        android:shadowColor="@android:color/black"
        android:shadowDx="-2"
        android:shadowDy="2"
        android:shadowRadius="0.01" />

</FrameLayout>