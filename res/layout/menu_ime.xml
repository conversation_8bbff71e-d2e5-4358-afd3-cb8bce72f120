<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res-auto"
    android:id="@+id/menu_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:importantForAccessibility="no"
    >
    <!-- Use width & height=match_parent for parent FrameLayout and buttons because they are placed
    inside a view that has a size controlled by weight. Ensure weight is large enough to support
    icon size. -->

    <com.android.systemui.statusbar.policy.KeyButtonView
        android:id="@+id/menu"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerInside"
        systemui:keyCode="82"
        systemui:playSound="false"
        android:visibility="invisible"
        android:contentDescription="@string/accessibility_menu"
        />
    <com.android.systemui.statusbar.policy.KeyButtonView
        android:id="@+id/ime_switcher"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible"
        android:contentDescription="@string/accessibility_ime_switch_button"
        android:scaleType="centerInside"
        />
    <com.android.systemui.statusbar.policy.KeyButtonView
        android:id="@+id/rotate_suggestion"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible"
        android:scaleType="centerInside"
        android:contentDescription="@string/accessibility_rotate_button"
        />
    <com.android.systemui.statusbar.policy.KeyButtonView
        android:id="@+id/accessibility_button"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible"
        android:contentDescription="@string/accessibility_accessibility_button"
        android:scaleType="centerInside"
    />
</FrameLayout>
