<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (C) 2017 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<!-- android:paddingHorizontal is set dynamically in SmartReplyView. -->
<Button xmlns:android="http://schemas.android.com/apk/res/android"
        style="@android:style/Widget.Material.Button"
        android:stateListAnimator="@null"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:minWidth="0dp"
        android:minHeight="@dimen/smart_reply_button_min_height"
        android:paddingVertical="@dimen/smart_reply_button_padding_vertical"
        android:background="@drawable/smart_reply_button_background"
        android:gravity="center"
        android:fontFamily="roboto-medium"
        android:textSize="@dimen/smart_reply_button_font_size"
        android:lineSpacingExtra="@dimen/smart_reply_button_line_spacing_extra"
        android:textColor="@color/smart_reply_button_text"
        android:textStyle="normal"
        android:ellipsize="none"/>
