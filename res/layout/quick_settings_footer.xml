<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:paddingBottom="@dimen/qs_tile_padding_top"
    android:paddingTop="@dimen/qs_tile_padding_top"
    android:paddingStart="@dimen/qs_footer_padding_start"
    android:paddingEnd="@dimen/qs_footer_padding_end"
    android:gravity="center_vertical"
    android:background="?android:attr/colorPrimary" >

    <TextView
        android:id="@+id/footer_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:layout_weight="1"
        android:textAppearance="@style/TextAppearance.QS.TileLabel"
        android:textColor="?android:attr/textColorSecondary"/>

    <ImageView
        android:id="@+id/footer_icon"
        android:layout_width="@dimen/qs_footer_icon_size"
        android:layout_height="@dimen/qs_footer_icon_size"
        android:contentDescription="@null"
        android:src="@drawable/ic_info_outline"
        android:tint="?android:attr/textColorSecondary"/>

</LinearLayout>
