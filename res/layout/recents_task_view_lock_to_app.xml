<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.systemui.statusbar.AlphaOptimizedFrameLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lock_to_app_fab"
    android:layout_width="@dimen/recents_lock_to_app_size"
    android:layout_height="@dimen/recents_lock_to_app_size"
    android:layout_gravity="bottom|end"
    android:layout_marginEnd="15dp"
    android:layout_marginBottom="15dp"
    android:translationZ="4dp"
    android:contentDescription="@string/recents_lock_to_app_button_label"
    android:background="@drawable/recents_lock_to_task_button_bg"
    android:visibility="invisible"
    android:alpha="0">
    <ImageView
        android:layout_width="@dimen/recents_lock_to_app_icon_size"
        android:layout_height="@dimen/recents_lock_to_app_icon_size"
        android:layout_gravity="center"
        android:src="@drawable/recents_lock_to_app_pin" />
</com.android.systemui.statusbar.AlphaOptimizedFrameLayout>