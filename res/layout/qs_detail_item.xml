<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/qs_detail_item_height"
    android:background="@drawable/btn_borderless_rect"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center_vertical"
    android:orientation="horizontal" >

    <ImageView
        android:id="@android:id/icon"
        android:layout_width="@dimen/qs_detail_item_icon_width"
        android:layout_height="@dimen/qs_detail_item_icon_size"
        android:layout_marginStart="@dimen/qs_detail_item_icon_marginStart"
        android:layout_marginEnd="@dimen/qs_detail_item_icon_marginEnd"
        android:tint="?android:attr/textColorPrimary"/>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_weight="1"
        android:orientation="vertical" >

        <TextView
            android:id="@android:id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textDirection="locale"
            android:ellipsize="end"
            android:textAppearance="@style/TextAppearance.QS.DetailItemPrimary" />

        <TextView
            android:id="@android:id/summary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textDirection="locale"
            android:layout_marginTop="2dp"
            android:textAppearance="@style/TextAppearance.QS.DetailItemSecondary" />
    </LinearLayout>

    <ImageView
        android:id="@android:id/icon2"
        style="@style/QSBorderlessButton"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:clickable="true"
        android:focusable="true"
        android:scaleType="center"
        android:contentDescription="@*android:string/media_route_controller_disconnect"
        android:tint="?android:attr/textColorPrimary" />

</LinearLayout>
