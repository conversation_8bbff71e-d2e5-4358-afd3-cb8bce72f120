<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2016 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/media_mirror"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:visibility="invisible"
    android:background="@color/status_bar_expanded_color">

    <com.android.systemui.statusbar.MediaClearView
        android:id="@+id/media_mirror_kill_view"
        style="@android:style/Widget.Material.Button.Borderless"
        android:layout_width="@dimen/notification_clear_all_size"
        android:layout_height="@dimen/notification_clear_all_size"
        android:background="@drawable/btn_clear_all"
        android:focusable="true"
        android:visibility="visible" />

    <com.android.systemui.qs.mi.MediaPlayerView
        android:id="@+id/qs_media_player_mirror_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
</FrameLayout>
