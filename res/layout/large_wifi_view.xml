<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/qs_media_player_view_width"
    android:layout_height="@dimen/qs_tile_large_height"
    android:id="@+id/qs_large_wifi_tile">

    <ImageView
        android:id="@+id/wifi_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/wifi_icon_margin_left"
        android:layout_marginTop="@dimen/wifi_icon_margin_left"
        android:src="@drawable/ic_qs_wifi" />

    <RelativeLayout
        android:id="@+id/text_and_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBaseline="@id/wifi_icon"
        android:layout_marginLeft="@dimen/wifi_text_view_margin_left"
        android:layout_marginTop="@dimen/wifi_text_view_margin_top"
        android:layout_toRightOf="@id/wifi_icon">

        <TextView
            android:id="@+id/wifi_name"
            android:layout_width="@dimen/wifi_text_view_text_width"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:text="@string/default_wifi_name"
            android:textColor="@color/color_white"
            android:textSize="@dimen/text_size_24"
            android:fontFamily="@string/font_sans_serif_medium"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:focusableInTouchMode="true"
            android:focusable="true"
            android:marqueeRepeatLimit="marquee_forever"/>

        <TextView
            android:id="@+id/wifi_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/wifi_name"
            android:layout_marginTop="@dimen/wifi_second_text_margin_top"
            android:text="@string/default_wifi_state"
            android:textColor="@color/color_white_40"
            android:textSize="@dimen/text_size_18"
            android:fontFamily="@string/font_sans_serif_medium"/>

    </RelativeLayout>
</RelativeLayout>