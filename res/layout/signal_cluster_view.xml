<?xml version="1.0" encoding="utf-8"?>
<!--
**
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License"); 
** you may not use this file except in compliance with the License. 
** You may obtain a copy of the License at 
**
**     http://www.apache.org/licenses/LICENSE-2.0 
**
** Unless required by applicable law or agreed to in writing, software 
** distributed under the License is distributed on an "AS IS" BASIS, 
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. 
** See the License for the specific language governing permissions and 
** limitations under the License.
*/
-->
<!-- extends LinearLayout -->
<com.android.systemui.statusbar.SignalClusterView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/signal_cluster"
    android:layout_height="match_parent"
    android:layout_width="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingEnd="@dimen/signal_cluster_battery_padding"
    >
    <ImageView
        android:id="@+id/vpn"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:paddingEnd="6dp"
        android:src="@drawable/stat_sys_vpn_ic"
        android:tint="@color/background_protect_secondary"
        android:contentDescription="@string/accessibility_vpn_on"
        />
    <FrameLayout
        android:id="@+id/ethernet_combo"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        >
        <com.android.systemui.statusbar.AlphaOptimizedImageView
            android:theme="?attr/lightIconTheme"
            android:id="@+id/ethernet"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            />
        <com.android.systemui.statusbar.AlphaOptimizedImageView
            android:theme="?attr/darkIconTheme"
            android:id="@+id/ethernet_dark"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:alpha="0.0"
            />
    </FrameLayout>
    <FrameLayout
        android:layout_height="17dp"
        android:layout_width="wrap_content">
        <ImageView
            android:id="@+id/wifi_in"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:src="@drawable/ic_activity_down"
            android:visibility="gone"
            android:paddingEnd="2dp"
            />
        <ImageView
            android:id="@+id/wifi_out"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:src="@drawable/ic_activity_up"
            android:paddingEnd="2dp"
            android:visibility="gone"
            />
    </FrameLayout>
    <FrameLayout
        android:id="@+id/wifi_combo"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        >
        <com.android.systemui.statusbar.AlphaOptimizedImageView
            android:theme="?attr/lightIconTheme"
            android:id="@+id/wifi_signal"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            />
        <com.android.systemui.statusbar.AlphaOptimizedImageView
            android:theme="?attr/darkIconTheme"
            android:id="@+id/wifi_signal_dark"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:alpha="0.0"
            />
        <ImageView
            android:id="@+id/wifi_inout"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            />
    </FrameLayout>
    <View
        android:id="@+id/wifi_signal_spacer"
        android:layout_width="@dimen/status_bar_wifi_signal_spacer_width"
        android:layout_height="4dp"
        android:visibility="gone"
        />
    <ViewStub
        android:id="@+id/connected_device_signals_stub"
        android:layout="@layout/connected_device_signal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
    <LinearLayout
        android:id="@+id/mobile_signal_group"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        >
    </LinearLayout>
    <View
        android:id="@+id/wifi_airplane_spacer"
        android:layout_width="@dimen/status_bar_airplane_spacer_width"
        android:layout_height="4dp"
        android:visibility="gone"
        />
    <ImageView
        android:id="@+id/airplane"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        />
</com.android.systemui.statusbar.SignalClusterView>
