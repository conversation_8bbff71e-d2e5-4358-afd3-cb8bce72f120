<?xml version="1.0" encoding="utf-8"?>
<!--
**
** Copyright 2016, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- Layout for {@link com.android.systemui.pip.tv.PipControlsView}. -->
<merge xmlns:android="http://schemas.android.com/apk/res/android">

    <com.android.systemui.pip.tv.PipControlButtonView
        android:id="@+id/full_button"
        android:layout_width="@dimen/picture_in_picture_button_width"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_fullscreen_white_24dp"
        android:text="@string/pip_fullscreen" />

    <com.android.systemui.pip.tv.PipControlButtonView
        android:id="@+id/close_button"
        android:layout_width="@dimen/picture_in_picture_button_width"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/picture_in_picture_button_start_margin"
        android:src="@drawable/ic_close_white"
        android:text="@string/pip_close" />

    <com.android.systemui.pip.tv.PipControlButtonView
        android:id="@+id/play_pause_button"
        android:layout_width="@dimen/picture_in_picture_button_width"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/picture_in_picture_button_start_margin"
        android:src="@drawable/ic_pause_white"
        android:text="@string/pip_pause"
        android:visibility="gone" />
</merge>
