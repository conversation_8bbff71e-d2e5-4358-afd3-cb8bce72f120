<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/toast_frame"
        android:minHeight="@dimen/picture_toast_height"
        android:orientation="horizontal"
        android:paddingStart="@dimen/picture_toast_text_horizontal_margin"
        android:paddingEnd="@dimen/picture_toast_text_horizontal_margin">

        <ImageView
            android:id="@+id/image"
            android:layout_width="@dimen/picture_toast_image_size"
            android:layout_height="@dimen/picture_toast_image_size"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/picture_toast_image_margin" />

        <TextView
            android:id="@+id/message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:fontFamily="@string/font_sans_serif_medium"
            android:singleLine="true"
            android:textColor="#de000000"
            android:textSize="30sp" />
    </LinearLayout>

</LinearLayout>


