<?xml version="1.0" encoding="utf-8"?>
<!--
**
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<com.android.keyguard.AlphaOptimizedLinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res-auto"
    android:id="@+id/mobile_combo"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:orientation="horizontal">
    <FrameLayout
        android:layout_height="17dp"
        android:layout_width="wrap_content"
        android:layout_gravity="center_vertical">
        <ImageView
            android:id="@+id/mobile_in"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:src="@drawable/ic_activity_down"
            android:visibility="gone"
            android:paddingEnd="2dp"
            />
        <ImageView
            android:id="@+id/mobile_out"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:src="@drawable/ic_activity_up"
            android:paddingEnd="2dp"
            android:visibility="gone"
            />
    </FrameLayout>
    <ImageView
        android:id="@+id/mobile_type"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:layout_gravity="center_vertical"
        android:paddingStart="1dp"
        android:paddingEnd="2dp"
        android:visibility="gone" />
    <Space
        android:id="@+id/mobile_roaming_space"
        android:layout_height="match_parent"
        android:layout_width="@dimen/roaming_icon_start_padding"
        android:visibility="gone"
    />
    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical">
        <com.android.systemui.statusbar.AnimatedImageView
            android:id="@+id/mobile_signal"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            systemui:hasOverlappingRendering="false"
            />
        <ImageView
            android:id="@+id/mobile_roaming"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/stat_sys_roaming"
            android:contentDescription="@string/data_connection_roaming"
            android:visibility="gone" />
    </FrameLayout>
</com.android.keyguard.AlphaOptimizedLinearLayout>
