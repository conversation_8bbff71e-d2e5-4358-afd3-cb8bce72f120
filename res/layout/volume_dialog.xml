<!--
     Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:theme="@style/qs_theme">
    <!-- right-aligned to be physically near volume button -->
    <LinearLayout
        android:id="@+id/volume_dialog"
        android:minWidth="@dimen/volume_dialog_panel_width"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|right"
        android:background="@android:color/transparent"
        android:paddingRight="@dimen/volume_dialog_panel_transparent_padding_right"
        android:paddingTop="@dimen/volume_dialog_panel_transparent_padding"
        android:paddingBottom="@dimen/volume_dialog_panel_transparent_padding"
        android:paddingLeft="@dimen/volume_dialog_panel_transparent_padding"
        android:orientation="vertical"
        android:clipToPadding="false" >

        <FrameLayout
            android:id="@+id/ringer"
            android:layout_width="@dimen/volume_dialog_ringer_size"
            android:layout_height="@dimen/volume_dialog_ringer_size"
            android:layout_marginBottom="@dimen/volume_dialog_spacer"
            android:translationZ="@dimen/volume_dialog_elevation"
            android:layout_gravity="right"
            android:clipToPadding="false"
            android:background="@drawable/rounded_bg_full">
            <com.android.keyguard.AlphaOptimizedImageButton
                android:id="@+id/ringer_icon"
                style="@style/VolumeButtons"
                android:background="@drawable/rounded_ripple"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:tint="@color/accent_tint_color_selector"
                android:layout_gravity="center"
                android:soundEffectsEnabled="false" />

            <include layout="@layout/volume_dnd_icon"
                     android:layout_width="match_parent"
                     android:layout_height="wrap_content"
                     android:layout_marginRight="@dimen/volume_dialog_stream_padding"
                     android:layout_marginTop="6dp"/>
        </FrameLayout>

        <LinearLayout
            android:id="@+id/main"
            android:layout_width="wrap_content"
            android:minWidth="@dimen/volume_dialog_panel_width"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:translationZ="@dimen/volume_dialog_elevation"
            android:clipToPadding="false"
            android:background="@drawable/rounded_bg_full" >
            <LinearLayout
                android:id="@+id/volume_dialog_rows"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/volume_dialog_panel_width"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingRight="@dimen/volume_dialog_stream_padding"
                android:paddingLeft="@dimen/volume_dialog_stream_padding">
                    <!-- volume rows added and removed here! :-) -->
            </LinearLayout>
            <FrameLayout
                android:id="@+id/settings_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/rounded_bg_bottom_background">
                <com.android.keyguard.AlphaOptimizedImageButton
                    android:id="@+id/settings"
                    android:src="@drawable/ic_settings_16dp"
                    android:layout_width="@dimen/volume_dialog_tap_target_size"
                    android:layout_height="@dimen/volume_dialog_tap_target_size"
                    android:layout_gravity="center"
                    android:contentDescription="@string/accessibility_volume_settings"
                    android:background="@drawable/ripple_drawable_20dp"
                    android:tint="?android:attr/textColorSecondary"
                    android:soundEffectsEnabled="false" />
            </FrameLayout>
        </LinearLayout>

    </LinearLayout>
</FrameLayout>