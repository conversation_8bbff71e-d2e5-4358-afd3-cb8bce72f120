<?xml version="1.0" encoding="utf-8"?>
<!--
**
** Copyright 2016, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<!-- Displays the RSSI status of a device that is connected via bluetooth. This layout is
     used for Auto. -->
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/connected_device_signals"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/status_bar_connected_device_signal_margin_end"
    android:visibility="gone" >
    <com.android.systemui.statusbar.AnimatedImageView
        android:theme="@style/DualToneLightTheme"
        android:id="@+id/connected_device_network_signal"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content" />
    <ImageView
        android:id="@+id/mobile_type"
        android:layout_height="@dimen/status_bar_connected_device_bt_indicator_size"
        android:layout_width="@dimen/status_bar_connected_device_bt_indicator_size"
        android:scaleType="centerInside"
        android:src="@drawable/car_stat_sys_data_bluetooth_indicator" />
</FrameLayout>
