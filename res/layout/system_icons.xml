<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2014 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/system_icons"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_vertical">

    <LinearLayout
        android:layout_width="@dimen/status_bar_extra_container_width"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/status_bar_extra_container_margin_end"
        android:gravity="end|center_vertical"
        android:orientation="horizontal">

        <include layout="@layout/extra_display_container" />
    </LinearLayout>

    <com.android.systemui.statusbar.phone.StatusIconContainer
        android:id="@+id/statusIcons"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/status_bar_icons_margin_end"
        android:layout_weight="1"
        android:gravity="center_horizontal|top"
        android:orientation="horizontal" />

<!--    <com.android.systemui.statusbar.policy.StatusBarClock-->
<!--        android:id="@+id/clock"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="match_parent"-->
<!--        android:layout_marginStart="@dimen/status_bar_clock_margin_end"-->
<!--        android:gravity="center_vertical|start"-->
<!--        android:singleLine="true"-->
<!--        android:textAppearance="@style/TextAppearance.StatusBar.Clock" />-->

    <com.android.systemui.BatteryMeterView
        android:id="@+id/battery"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/status_bar_battery_margin_end"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:visibility="gone" />
</LinearLayout>