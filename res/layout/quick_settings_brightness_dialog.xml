<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2012 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res-auto"
    android:layout_height="232dp"
    android:layout_width="104dp"
    android:layout_gravity="center_vertical"
    android:paddingLeft="16dp"
    android:paddingRight="16dp"
    style="@style/BrightnessDialogContainer">

    <ImageView
        android:id="@+id/brightness_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="8dp"
        android:src="@drawable/ic_qs_brightness_auto_off"
        android:contentDescription="@null"
        android:visibility="gone" />

    <com.android.systemui.settings.ToggleSliderView
        android:id="@+id/brightness_slider"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:contentDescription="@string/accessibility_brightness"
        android:importantForAccessibility="no"
        systemui:text="@string/status_bar_settings_auto_brightness_label" />

</LinearLayout>
