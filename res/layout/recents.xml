<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Recents View -->
    <com.android.systemui.recents.views.RecentsView
        android:id="@+id/recents_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    </com.android.systemui.recents.views.RecentsView>

    <!-- Incompatible task overlay -->
    <ViewStub android:id="@+id/incompatible_app_overlay_stub"
        android:inflatedId="@+id/incompatible_app_overlay"
        android:layout="@layout/recents_incompatible_app_overlay"
        android:layout_width="match_parent"
        android:layout_height="128dp"
        android:layout_gravity="center_horizontal|top" />

    <!-- Nav Bar Scrim View -->
    <ImageView
        android:id="@+id/nav_bar_scrim"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|bottom"
        android:scaleType="fitXY"
        android:src="@drawable/recents_lower_gradient" />
</FrameLayout>
