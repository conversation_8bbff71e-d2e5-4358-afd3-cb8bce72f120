<?xml version="1.0" encoding="utf-8"?>
<!--
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
-->
<com.android.systemui.RegionInterceptingFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/left"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_gravity="left|top"
        android:tint="#ff000000"
        android:src="@drawable/rounded" />
    <ImageView
        android:id="@+id/right"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:tint="#ff000000"
        android:layout_gravity="right|bottom"
        android:src="@drawable/rounded" />
</com.android.systemui.RegionInterceptingFrameLayout>
