<?xml version="1.0" encoding="utf-8"?>
<!--
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
-->

<!-- Extends RelativeLayout -->
<com.android.systemui.qs.QuickStatusBarHeader
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/header"
    android:layout_width="match_parent"
    android:layout_height="@*android:dimen/quick_qs_total_height"
    android:layout_gravity="@integer/notification_panel_layout_gravity"
    android:background="@android:color/transparent"
    android:baselineAligned="false"
    android:clickable="false"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:paddingTop="0dp"
    android:paddingEnd="0dp"
    android:paddingStart="0dp"
    android:elevation="4dp" >

    <include layout="@layout/quick_status_bar_header_system_icons" />

    <!-- Status icons within the panel itself (and not in the top-most status bar) -->
    <include layout="@layout/quick_qs_status_icons" />

    <!-- Layout containing tooltips, alarm text, etc. -->
    <include layout="@layout/quick_settings_header_info" />

    <com.android.systemui.qs.QuickQSPanel
        android:id="@+id/quick_qs_panel"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_below="@id/quick_qs_status_icons"
        android:layout_marginStart="@dimen/qs_header_tile_margin_horizontal"
        android:layout_marginEnd="@dimen/qs_header_tile_margin_horizontal"
        android:accessibilityTraversalAfter="@+id/date_time_group"
        android:accessibilityTraversalBefore="@id/expand_indicator"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:focusable="true"
        android:importantForAccessibility="yes" />

    <com.android.systemui.statusbar.AlphaOptimizedImageView
        android:id="@+id/qs_detail_header_progress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:alpha="0"
        android:background="@color/qs_detail_progress_track"
        android:src="@drawable/indeterminate_anim"/>

    <TextView
        android:id="@+id/header_debug_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:fontFamily="sans-serif-condensed"
        android:padding="2dp"
        android:textColor="#00A040"
        android:textSize="11dp"
        android:textStyle="bold"
        android:visibility="invisible"/>

</com.android.systemui.qs.QuickStatusBarHeader>
