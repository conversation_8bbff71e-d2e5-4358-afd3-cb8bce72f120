<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.keyguard.AlphaOptimizedLinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="@dimen/qs_detail_header_padding"
    android:paddingTop="@dimen/qs_detail_header_padding"
    android:paddingBottom="@dimen/qs_detail_items_padding_top"
    android:paddingEnd="@dimen/qs_panel_padding"
    android:background="@drawable/btn_borderless_rect"
    android:orientation="vertical"
    android:gravity="center">

    <com.android.systemui.ResizingSpace
        android:layout_width="match_parent"
        android:layout_height="@dimen/qs_detail_margin_top" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@android:id/title"
            android:paddingStart="@dimen/qs_detail_header_text_padding"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textDirection="locale"
            android:textAppearance="@style/TextAppearance.QS.DetailHeader" />

        <ImageView
            android:id="@+id/settings"
            android:layout_width="@dimen/qs_detail_image_width"
            android:layout_height="@dimen/qs_detail_image_height"
            android:background="?android:attr/selectableItemBackground"
            android:padding="@dimen/qs_detail_image_padding"
            android:src="@drawable/ic_settings"
            android:visibility="gone"/>

        <Switch
            android:id="@android:id/toggle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:textAppearance="@style/TextAppearance.QS.DetailHeader" />

    </LinearLayout>

</com.android.keyguard.AlphaOptimizedLinearLayout>
