<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2017 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<!-- Loaded into BatteryMeterView as necessary -->
<TextView
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/battery_percentage_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="invisible"
        android:singleLine="true"
        android:textAppearance="@style/TextAppearance.StatusBar.Expanded.Clock"
        android:textColor="?android:attr/textColorPrimary"
        android:gravity="center_vertical|start"
        android:paddingStart="@dimen/battery_level_padding_start"
        android:importantForAccessibility="no"
        />
