<!--
     Copyright (C) 2018 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<androidx.car.widget.PagedListView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/car_card_rounded_background"
    android:id="@+id/volume_list"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/car_margin"
    android:layout_marginEnd="@dimen/car_margin"
    android:minWidth="@dimen/volume_dialog_panel_width"
    android:theme="@style/Theme.Car.NoActionBar"
    app:dividerStartMargin="@dimen/car_keyline_1"
    app:dividerEndMargin="@dimen/car_keyline_1"
    app:gutter="none"
    app:showPagedListViewDivider="true"
    app:scrollBarEnabled="false" />
