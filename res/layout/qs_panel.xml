<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.systemui.qs.QSContainerImpl
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/quick_settings_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipToPadding="false"
    android:clipChildren="false" >

    <!--&lt;!&ndash; Main QS background &ndash;&gt;-->
    <!--<View-->
        <!--android:id="@+id/quick_settings_background"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="0dp"-->
        <!--android:elevation="4dp"-->
        <!--android:background="@drawable/qs_background_primary" />-->

    <!--&lt;!&ndash; Black part behind the status bar &ndash;&gt;-->
    <!--<View-->
        <!--android:id="@+id/quick_settings_status_bar_background"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="@*android:dimen/quick_qs_offset_height"-->
        <!--android:clipToPadding="false"-->
        <!--android:clipChildren="false"-->
        <!--android:background="#ff000000" />-->

    <!--&lt;!&ndash; Gradient view behind QS &ndash;&gt;-->
    <!--<View-->
        <!--android:id="@+id/quick_settings_gradient_view"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="126dp"-->
        <!--android:layout_marginTop="@*android:dimen/quick_qs_offset_height"-->
        <!--android:clipToPadding="false"-->
        <!--android:clipChildren="false"-->
        <!--android:background="@drawable/qs_bg_gradient" />-->

    <!--
    <View
        android:id="@+id/view_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80FFFFFF"/>  -->
<!--    <com.android.systemui.qs.CtPhoneView-->
<!--        android:id="@+id/quick_settings_ct_telephone_number"-->
<!--        android:layout_marginTop="30dp"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_marginLeft="65dp"-->
<!--        android:layout_height="40dp"-->
<!--        android:textSize="22sp"-->
<!--        android:gravity="right"-->
<!--        android:textColor="@android:color/white"-->
<!--        />-->
    <com.android.systemui.qs.QSPanel
        android:id="@+id/quick_settings_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/quick_settings_container_margin_top"
        android:focusable="true"
        android:accessibilityTraversalBefore="@id/qs_carrier_text" />

    <include layout="@layout/quick_status_bar_expanded_header" />

    <include layout="@layout/qs_footer_impl" />

<!--    <include android:id="@+id/qs_detail"-->
<!--        layout="@layout/qs_detail" />-->

<!--    <include android:id="@+id/qs_customize"-->
<!--        layout="@layout/qs_customize_panel"-->
<!--        android:visibility="gone"/>-->

</com.android.systemui.qs.QSContainerImpl>
