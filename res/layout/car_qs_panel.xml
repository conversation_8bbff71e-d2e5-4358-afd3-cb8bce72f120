<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/quick_settings_container"
    android:clipChildren="false"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/car_qs_background_primary"
    android:orientation="vertical"
    android:elevation="4dp"
    android:theme="@android:style/Theme">

    <include layout="@layout/car_status_bar_header"/>
    <include layout="@layout/car_qs_footer"/>

    <RelativeLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/user_switcher_container"
        android:clipChildren="false"
        android:layout_width="match_parent"
        android:layout_height="@dimen/car_user_switcher_container_height">

        <com.android.systemui.statusbar.car.UserGridRecyclerView
            android:id="@+id/user_grid"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:dayNightStyle="always_light"
            app:showPagedListViewDivider="false"
            app:gutter="both"
            app:itemSpacing="@dimen/car_user_switcher_vertical_spacing_between_users"/>

    </RelativeLayout>

</LinearLayout>
