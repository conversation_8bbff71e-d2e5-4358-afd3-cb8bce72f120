<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<TextView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/button"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingStart="14dp"
    android:paddingEnd="14dp"
    android:paddingTop="12dp"
    android:paddingBottom="12dp"
    android:text="@string/recents_stack_action_button_label"
    android:textSize="14sp"
    android:textColor="?attr/wallpaperTextColor"
    android:textAllCaps="true"
    android:shadowColor="#99000000"
    android:shadowDx="0"
    android:shadowDy="2"
    android:shadowRadius="5"
    android:fontFamily="sans-serif-medium"
    android:background="@drawable/recents_stack_action_background"
    android:visibility="invisible"
    android:forceHasOverlappingRendering="false"
    style="?attr/clearAllStyle" />
