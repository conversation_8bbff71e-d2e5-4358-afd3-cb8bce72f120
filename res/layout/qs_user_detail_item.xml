<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (C) 2014 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<!-- LinearLayout -->
<com.android.systemui.qs.tiles.UserDetailItemView
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:systemui="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="top|center_horizontal"
        android:paddingTop="16dp"
        android:minHeight="112dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:focusable="true"
        android:background="@drawable/ripple_drawable"
        systemui:activatedFontFamily="sans-serif-medium">

    <com.android.systemui.statusbar.phone.UserAvatarView
            android:id="@+id/user_picture"
            android:layout_width="@dimen/framed_avatar_size"
            android:layout_height="@dimen/framed_avatar_size"
            android:layout_marginBottom="7dp"
            android:backgroundTint="@color/qs_user_detail_avatar_tint"
            android:backgroundTintMode="src_atop"
            systemui:frameWidth="2dp"
            systemui:framePadding="2.5dp"
            systemui:badgeDiameter="18dp"
            systemui:badgeMargin="1dp"
            systemui:frameColor="@color/qs_user_detail_avatar_frame"/>

    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
        <TextView
                android:id="@+id/user_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/qs_detail_item_secondary_text_size"
                android:textColor="?android:attr/textColorSecondary"
                android:gravity="center_horizontal" />
        <ImageView
                android:id="@+id/restricted_padlock"
                android:layout_width="@dimen/qs_detail_item_secondary_text_size"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:src="@drawable/ic_info"
                android:layout_marginLeft="@dimen/restricted_padlock_pading"
                android:scaleType="centerInside"
                android:visibility="gone" />
    </LinearLayout>

</com.android.systemui.qs.tiles.UserDetailItemView>
