<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2008 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- RelativeLayouts have an issue enforcing minimum heights, so just
     work around this for now with LinearLayouts. -->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/size_154"
    android:layout_height="@dimen/size_203"
    android:layout_gravity="center"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:id="@*android:id/icon"
        android:layout_width="@dimen/size_86"
        android:layout_height="@dimen/size_86"
        android:layout_gravity="center"
        android:scaleType="center"
        android:alpha="?android:attr/primaryContentAlpha"
    />

    <TextView
        android:id="@*android:id/message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center_horizontal"
        android:paddingTop="10dp"
        android:gravity="center"
        android:textSize="@dimen/text_size_21"
        android:textColor="@color/color_3A35FF"
        android:textAppearance="?android:attr/textAppearanceSmall"
        />

    <TextView
        android:id="@*android:id/status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center_horizontal"
        android:gravity="center"
        android:textColor="?android:attr/textColorTertiary"
        android:textAppearance="?android:attr/textAppearanceSmall"
        android:visibility="gone"
        />
</LinearLayout>
