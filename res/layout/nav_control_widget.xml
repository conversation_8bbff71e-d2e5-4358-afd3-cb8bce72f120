<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright (C) 2016 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:gravity="center"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/width"
        android:layout_width="48dp"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_width"
        android:clickable="true"
        android:tint="?android:attr/textColorPrimary" />

    <View
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:background="?android:attr/listDivider" />

    <ImageView
        android:id="@+id/close"
        android:layout_width="48dp"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_close"
        android:clickable="true"
        android:tint="?android:attr/textColorPrimary" />

    <View
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:background="?android:attr/listDivider" />

    <ImageView
        android:id="@+id/drag"
        android:layout_width="48dp"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_drag_handle"
        android:clickable="true"
        android:tint="?android:attr/textColorPrimary" />

</LinearLayout>
