<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2018 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/header_text_container"
    android:layout_width="match_parent"
    android:layout_height="@dimen/qs_header_tooltip_height"
    android:layout_below="@id/quick_status_bar_system_icons"
    android:visibility="gone"
    android:layout_marginTop="12dp">

    <TextView
        android:id="@+id/long_press_tooltip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|bottom"
        android:alpha="0"
        android:text="@string/quick_settings_header_onboarding_text"
        android:textAppearance="@style/TextAppearance.QS.TileLabel"
        android:textColor="?android:attr/textColorSecondary"
        android:visibility="invisible" />

    <LinearLayout
        android:id="@+id/status_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|bottom"
        android:gravity="center_vertical"
        android:visibility="invisible">

        <ImageView
            android:id="@+id/next_alarm_icon"
            android:layout_width="@dimen/qs_header_alarm_icon_size"
            android:layout_height="@dimen/qs_header_alarm_icon_size"
            android:src="@drawable/stat_sys_alarm"
            android:tint="?android:attr/textColorPrimary"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/next_alarm_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/qs_header_alarm_text_margin_start"
            android:textAppearance="@style/TextAppearance.QS.TileLabel"
            android:visibility="gone"/>

        <View
            android:id="@+id/status_separator"
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:background="@android:color/white"
            android:backgroundTint="?android:attr/textColorPrimary"
            android:visibility="gone"/>

        <ImageView
            android:id="@+id/ringer_mode_icon"
            android:layout_width="@dimen/qs_header_alarm_icon_size"
            android:layout_height="@dimen/qs_header_alarm_icon_size"
            android:tint="?android:attr/textColorPrimary"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/ringer_mode_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/qs_header_alarm_text_margin_start"
            android:textAppearance="@style/TextAppearance.QS.TileLabel"
            android:visibility="gone"/>

    </LinearLayout>

</FrameLayout>
