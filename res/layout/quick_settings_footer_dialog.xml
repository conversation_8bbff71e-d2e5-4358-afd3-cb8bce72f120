<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/scrollView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipToPadding="false">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="?android:attr/dialogPreferredPadding"
        android:paddingRight="?android:attr/dialogPreferredPadding"
        android:paddingLeft="?android:attr/dialogPreferredPadding"
        android:orientation="vertical">
        <LinearLayout
            android:id="@+id/device_management_disclosures"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="?android:attr/dialogPreferredPadding"
            android:orientation="vertical">
            <TextView
                android:id="@+id/device_management_subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/monitoring_title_device_owned"
                style="@android:style/TextAppearance.Material.Title"
                android:textColor="?android:attr/textColorPrimary"
                android:paddingBottom="@dimen/qs_footer_dialog_subtitle_padding"
            />
            <TextView
                android:id="@+id/device_management_warning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@null"
                style="@android:style/TextAppearance.Material.Subhead"
                android:textColor="?android:attr/textColorPrimary"
            />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ca_certs_disclosures"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="?android:attr/dialogPreferredPadding"
            android:orientation="vertical">
            <TextView
                android:id="@+id/ca_certs_subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/monitoring_subtitle_ca_certificate"
                style="@android:style/TextAppearance.Material.Title"
                android:textColor="?android:attr/textColorPrimary"
                android:paddingBottom="@dimen/qs_footer_dialog_subtitle_padding"
            />
            <TextView
                android:id="@+id/ca_certs_warning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@null"
                style="@android:style/TextAppearance.Material.Subhead"
                android:textColor="?android:attr/textColorPrimary"
            />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/network_logging_disclosures"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="?android:attr/dialogPreferredPadding"
            android:orientation="vertical">
            <TextView
                android:id="@+id/network_logging_subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/monitoring_subtitle_network_logging"
                style="@android:style/TextAppearance.Material.Title"
                android:textColor="?android:attr/textColorPrimary"
                android:paddingBottom="@dimen/qs_footer_dialog_subtitle_padding"
            />
            <TextView
                android:id="@+id/network_logging_warning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@null"
                style="@android:style/TextAppearance.Material.Subhead"
                android:textColor="?android:attr/textColorPrimary"
            />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/vpn_disclosures"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="?android:attr/dialogPreferredPadding"
            android:orientation="vertical">
            <TextView
                android:id="@+id/vpn_subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/monitoring_subtitle_vpn"
                style="@android:style/TextAppearance.Material.Title"
                android:textColor="?android:attr/textColorPrimary"
                android:paddingBottom="@dimen/qs_footer_dialog_subtitle_padding"
            />
            <TextView
                android:id="@+id/vpn_warning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@null"
                style="@android:style/TextAppearance.Material.Subhead"
                android:textColor="?android:attr/textColorPrimary"
            />
        </LinearLayout>
    </LinearLayout>
</ScrollView>
