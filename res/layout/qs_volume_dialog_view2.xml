<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/mi_volume_bg"
    android:gravity="center_vertical"
    android:id="@+id/volume_container">

    <RelativeLayout
        android:id="@+id/rl_volume_media"
        android:layout_width="@dimen/qs_volume_unroll_bar_width"
        android:layout_height="@dimen/qs_volume_unroll_bar_height"
        android:layout_alignParentRight="true"
        android:layout_marginRight="50dp">

        <com.android.systemui.qs.mi.VerticalSeekBar
            android:id="@+id/volume_slider"
            android:layout_width="@dimen/qs_volume_unroll_bar_width"
            android:layout_height="@dimen/qs_volume_unroll_bar_height"
            android:minWidth="@dimen/qs_volume_unroll_bar_width"
            android:maxWidth="@dimen/qs_volume_unroll_bar_width"
            android:minHeight="@dimen/qs_volume_unroll_bar_height"
            android:maxHeight="@dimen/qs_volume_unroll_bar_height"
            android:progressDrawable="@drawable/volume_unroll_bar_background"
            android:thumb="@null"
            android:focusable="false" />

        <ImageView
            android:id="@+id/volume_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/menu_media"
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="@dimen/qs_tile_margin" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="@dimen/qs_volume_unroll_bar_width"
        android:layout_height="@dimen/qs_volume_unroll_bar_height"
        android:layout_toLeftOf="@id/rl_volume_media"
        android:layout_marginRight="30dp">

        <com.android.systemui.qs.mi.VerticalSeekBar
            android:id="@+id/volume_alarm_slider"
            android:layout_width="@dimen/qs_volume_unroll_bar_width"
            android:layout_height="@dimen/qs_volume_unroll_bar_height"
            android:minWidth="@dimen/qs_volume_unroll_bar_width"
            android:maxWidth="@dimen/qs_volume_unroll_bar_width"
            android:minHeight="@dimen/qs_volume_unroll_bar_height"
            android:maxHeight="@dimen/qs_volume_unroll_bar_height"
            android:progressDrawable="@drawable/volume_unroll_bar_background"
            android:thumb="@null"
            android:focusable="false" />

        <ImageView
            android:id="@+id/clock_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/menu_clock"
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="@dimen/qs_tile_margin" />

    </RelativeLayout>

</RelativeLayout>
