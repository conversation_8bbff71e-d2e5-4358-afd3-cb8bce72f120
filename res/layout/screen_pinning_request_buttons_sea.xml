<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright (c) 2014, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->
<!-- Note all width/height dimensions are switched here to handle landspace
     rather than duplicating them all.
     This layout matches the structure of navigation_bar.xml (rot90) and
     will need to be kept up to sync with changes there.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/screen_pinning_buttons"
    android:layout_height="match_parent"
    android:layout_width="@dimen/screen_pinning_request_button_height"
    android:background="?android:attr/colorAccent"
    android:orientation="vertical">

    <View
        android:layout_height="@dimen/screen_pinning_request_side_width"
        android:layout_width="match_parent"
        android:layout_weight="0"
        android:visibility="invisible" />

    <FrameLayout
        android:id="@+id/screen_pinning_back_group"
        android:layout_height="@dimen/screen_pinning_request_button_width"
        android:layout_width="@dimen/screen_pinning_request_button_height"
        android:layout_weight="0"
        android:theme="@*android:style/ThemeOverlay.DeviceDefault.Accent">

        <ImageView
            android:id="@+id/screen_pinning_back_bg_light"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:scaleType="matrix"
            android:layout_marginLeft="@dimen/screen_pinning_request_seascape_padding_negative"
            android:src="@drawable/screen_pinning_light_bg_circ" />

        <ImageView
            android:id="@+id/screen_pinning_back_bg"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:scaleType="matrix"
            android:layout_marginLeft="@dimen/screen_pinning_request_seascape_button_offset"
            android:paddingRight="@dimen/screen_pinning_request_inner_padding"
            android:paddingTop="@dimen/screen_pinning_request_inner_padding"
            android:paddingBottom="@dimen/screen_pinning_request_inner_padding"
            android:src="@drawable/screen_pinning_bg_circ" />

        <ImageView
            android:id="@+id/screen_pinning_back_icon"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:scaleType="center"
            android:paddingRight="@dimen/screen_pinning_request_nav_icon_padding"
            android:paddingTop="@dimen/screen_pinning_request_nav_side_padding"
            android:paddingBottom="@dimen/screen_pinning_request_nav_side_padding"
            android:src="@drawable/ic_sysbar_back" />
    </FrameLayout>

    <View
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        android:layout_weight="1"
        android:visibility="invisible" />

    <FrameLayout
        android:id="@+id/screen_pinning_home_group"
        android:layout_height="@dimen/screen_pinning_request_button_width"
        android:layout_width="@dimen/screen_pinning_request_button_height"
        android:layout_weight="0"
        android:theme="@*android:style/ThemeOverlay.DeviceDefault.Accent" >

        <ImageView
            android:id="@+id/screen_pinning_home_bg_light"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:scaleType="matrix"
            android:layout_marginLeft="@dimen/screen_pinning_request_seascape_padding_negative"
            android:src="@drawable/screen_pinning_light_bg_circ" />

        <ImageView
            android:id="@+id/screen_pinning_home_bg"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:scaleType="matrix"
            android:layout_marginLeft="@dimen/screen_pinning_request_seascape_button_offset"
            android:paddingRight="@dimen/screen_pinning_request_inner_padding"
            android:paddingTop="@dimen/screen_pinning_request_inner_padding"
            android:paddingBottom="@dimen/screen_pinning_request_inner_padding"
            android:src="@drawable/screen_pinning_bg_circ" />

        <ImageView
            android:id="@+id/screen_pinning_home_icon"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:scaleType="center"
            android:paddingRight="@dimen/screen_pinning_request_nav_icon_padding"
            android:paddingTop="@dimen/screen_pinning_request_nav_side_padding"
            android:paddingBottom="@dimen/screen_pinning_request_nav_side_padding"
            android:src="@drawable/ic_sysbar_home" />
    </FrameLayout>

    <View
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        android:layout_weight="1"
        android:visibility="invisible" />

    <FrameLayout
        android:id="@+id/screen_pinning_recents_group"
        android:layout_height="@dimen/screen_pinning_request_button_width"
        android:layout_width="@dimen/screen_pinning_request_button_height"
        android:layout_weight="0"
        android:theme="@*android:style/ThemeOverlay.DeviceDefault.Accent">

        <ImageView
            android:id="@+id/screen_pinning_recents_bg_light"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:scaleType="matrix"
            android:layout_marginLeft="@dimen/screen_pinning_request_seascape_padding_negative"
            android:src="@drawable/screen_pinning_light_bg_circ" />

        <ImageView
            android:id="@+id/screen_pinning_recents_bg"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:scaleType="matrix"
            android:layout_marginLeft="@dimen/screen_pinning_request_seascape_button_offset"
            android:paddingRight="@dimen/screen_pinning_request_inner_padding"
            android:paddingTop="@dimen/screen_pinning_request_inner_padding"
            android:paddingBottom="@dimen/screen_pinning_request_inner_padding"
            android:src="@drawable/screen_pinning_bg_circ" />

        <ImageView
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:scaleType="center"
            android:paddingRight="@dimen/screen_pinning_request_nav_icon_padding"
            android:paddingTop="@dimen/screen_pinning_request_nav_side_padding"
            android:paddingBottom="@dimen/screen_pinning_request_nav_side_padding"
            android:src="@drawable/ic_sysbar_recent" />
    </FrameLayout>

    <View
        android:layout_height="@dimen/screen_pinning_request_side_width"
        android:layout_width="match_parent"
        android:layout_weight="0"
        android:visibility="invisible" />

</LinearLayout>
