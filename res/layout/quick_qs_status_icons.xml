<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res-auto"
    android:id="@+id/quick_qs_status_icons"
    android:layout_width="match_parent"
    android:layout_height="@dimen/quick_qs_status_icons_height"
    android:layout_marginTop="18dp"
    android:layout_marginBottom="14dp"
    android:layout_marginStart="@dimen/status_bar_padding_start"
    android:layout_marginEnd="@dimen/status_bar_padding_end"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:minHeight="20dp" >

    <com.android.systemui.statusbar.phone.StatusIconContainer
        android:id="@+id/statusIcons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1" />

    <com.android.systemui.BatteryMeterView
        android:id="@+id/battery"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:layout_marginLeft="@dimen/quick_qs_status_icons_battery_margin_left"
        android:gravity="center_vertical"
        systemui:showDark="true" />

</LinearLayout>
