<!--
     Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:tag="row"
    android:layout_height="wrap_content"
    android:layout_width="@dimen/volume_dialog_panel_width"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:theme="@style/qs_theme">

    <LinearLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:gravity="center"
        android:layout_gravity="center"
        android:orientation="vertical" >
        <TextView
            android:id="@+id/volume_row_header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLength="10"
            android:maxLines="1"
            android:visibility="gone"
            android:textColor="?android:attr/colorControlNormal"
            android:textAppearance="@style/TextAppearance.Volume.Header" />
        <FrameLayout
            android:id="@+id/volume_row_slider_frame"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/volume_dialog_slider_margin_top"
            android:layout_marginBottom="@dimen/volume_dialog_slider_margin_bottom"
            android:layoutDirection="rtl"
            android:layout_height="@dimen/volume_dialog_slider_height">
            <SeekBar
                android:id="@+id/volume_row_slider"
                android:clickable="true"
                android:layout_width="@dimen/volume_dialog_slider_height"
                android:layout_height="match_parent"
                android:layoutDirection="rtl"
                android:layout_gravity="center"
                android:rotation="90" />
        </FrameLayout>

        <com.android.keyguard.AlphaOptimizedImageButton
            android:id="@+id/volume_row_icon"
            style="@style/VolumeButtons"
            android:layout_width="@dimen/volume_dialog_tap_target_size"
            android:layout_height="@dimen/volume_dialog_tap_target_size"
            android:background="@drawable/ripple_drawable_20dp"
            android:layout_marginBottom="@dimen/volume_dialog_row_margin_bottom"
            android:tint="@color/accent_tint_color_selector"
            android:soundEffectsEnabled="false" />
    </LinearLayout>

    <include layout="@layout/volume_dnd_icon"/>

</FrameLayout>
