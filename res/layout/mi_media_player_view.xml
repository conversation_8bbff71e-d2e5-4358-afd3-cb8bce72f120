<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/media_relative_layout"
    android:layout_width="@dimen/qs_media_player_view_width"
    android:layout_height="@dimen/qs_media_player_view_height"
    android:layout_marginLeft="@dimen/qs_panel_media_left_margin"
    android:gravity="center_vertical"
    android:background="@drawable/ic_qs_rectangle">

    <android.support.v7.widget.CardView
        android:id="@+id/music_guide_card"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/mi_media_cover_left_margin"
        android:layout_marginEnd="@dimen/mi_media_right_margin"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="@dimen/mi_media_cover_radius">
        <ImageView
            android:id="@+id/music_guide"
            android:layout_width="@dimen/mi_media_cover_size"
            android:layout_height="@dimen/mi_media_cover_size"
            android:layout_gravity="center_vertical"
            android:src="@drawable/media_default_icon" />
    </android.support.v7.widget.CardView>

    <ImageView
        android:id="@+id/next_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/menu_next_btn"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/mi_media_next_play_btn_right_margin"/>

    <ImageView
        android:id="@+id/play_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@id/next_btn"
        android:layout_centerVertical="true"
        android:src="@drawable/menu_pause_btn"
        android:layout_marginEnd="@dimen/mi_media_right_margin"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@id/next_btn"
        android:layout_toEndOf="@id/music_guide_card"
        android:layout_centerVertical="true"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <com.android.systemui.qs.mi.MarqueeTextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:textSize="@dimen/text_size_24"
            android:textColor="@color/color_white"
            android:text="@string/qs_player_title_default"/>

        <com.android.systemui.qs.mi.MarqueeTextView
            android:id="@+id/artist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/qs_media_desc_padding_top"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:textSize="@dimen/text_size_18"
            android:textColor="@color/color_white_40"
            android:visibility="gone"/>
    </LinearLayout>

</RelativeLayout>
