<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (C) 2014 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<!-- LinearLayout -->
<com.android.systemui.qs.tiles.UserDetailItemView
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:sysui="http://schemas.android.com/apk/res-auto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="8dp"
        android:layout_marginEnd="8dp"
        android:gravity="center_vertical"
        android:clickable="true"
        android:background="@drawable/ripple_drawable"
        android:clipChildren="false"
        android:clipToPadding="false"
        sysui:activatedFontFamily="sans-serif-medium">
    <TextView android:id="@+id/user_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="13dp"
            android:textAppearance="@style/TextAppearance.StatusBar.Expanded.UserSwitcher.UserName"
            />
    <com.android.systemui.statusbar.phone.UserAvatarView android:id="@+id/user_picture"
            android:layout_width="@dimen/framed_avatar_size"
            android:layout_height="@dimen/framed_avatar_size"
            android:contentDescription="@null"
            android:backgroundTint="@color/qs_user_detail_avatar_tint"
            android:backgroundTintMode="src_atop"
            sysui:frameWidth="@dimen/keyguard_user_switcher_border_thickness"
            sysui:framePadding="2.5dp"
            sysui:badgeDiameter="18dp"
            sysui:badgeMargin="1dp"
            sysui:frameColor="@color/qs_user_detail_avatar_frame" />
</com.android.systemui.qs.tiles.UserDetailItemView>
