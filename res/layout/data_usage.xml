<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.systemui.qs.tiles.DataUsageDetailView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="16dp"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:orientation="vertical" >

    <TextView
        android:id="@android:id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/TextAppearance.QS.DataUsage" />

    <TextView
        android:id="@+id/usage_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/TextAppearance.QS.DataUsage.Usage" />

    <com.android.systemui.qs.DataUsageGraph
        android:id="@+id/usage_graph"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_marginBottom="@dimen/qs_panel_padding"
        android:layout_marginTop="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal" >

        <TextView
            android:id="@+id/usage_carrier_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textAppearance="@style/TextAppearance.QS.DataUsage" />

        <TextView
            android:id="@+id/usage_info_top_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textAppearance="@style/TextAppearance.QS.DataUsage" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:orientation="horizontal" >

        <TextView
            android:id="@+id/usage_period_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textAppearance="@style/TextAppearance.QS.DataUsage.Secondary" />

        <TextView
            android:id="@+id/usage_info_bottom_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textAppearance="@style/TextAppearance.QS.DataUsage.Secondary" />
    </LinearLayout>

    <TextView
        android:id="@+id/roaming_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="16dp"
        android:text="@string/data_connection_roaming"
        android:textAppearance="@style/TextAppearance.QS.DataUsage.Secondary"
        android:visibility="gone" />

</com.android.systemui.qs.tiles.DataUsageDetailView>
