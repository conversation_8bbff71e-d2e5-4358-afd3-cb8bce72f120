<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res-auto"
    android:id="@+id/date_time_alarm_group"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="4dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:background="?android:attr/selectableItemBackground">

    <com.android.systemui.statusbar.policy.DateView
        android:id="@+id/date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textAppearance="@style/TextAppearance.StatusBar.Expanded.Date"
        android:textSize="@dimen/qs_time_collapsed_size"
        android:gravity="center_vertical"
        systemui:datePattern="@string/abbrev_wday_month_day_no_year_alarm" />

    <com.android.systemui.statusbar.AlphaOptimizedImageView
        android:id="@+id/alarm_status_collapsed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_access_alarms_small"
        android:tint="?android:attr/textColorPrimary"
        android:paddingStart="6dp"
        android:paddingEnd="6dp"
        android:gravity="center"
        android:visibility="gone" />

    <com.android.systemui.statusbar.AlphaOptimizedButton
        android:id="@+id/alarm_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/TextAppearance.StatusBar.Expanded.Clock"
        android:gravity="center_vertical"
        android:background="@null"
        android:clickable="false"
        android:visibility="gone" />

</LinearLayout>
