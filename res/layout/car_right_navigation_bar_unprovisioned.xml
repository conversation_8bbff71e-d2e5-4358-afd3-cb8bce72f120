<?xml version="1.0" encoding="utf-8"?>
<!--
**
** Copyright 2018, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<com.android.systemui.statusbar.car.CarNavigationBarView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:background="@drawable/system_bar_background">

    <LinearLayout
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        android:id="@+id/nav_buttons"
        android:orientation="vertical"
        android:gravity="top"
        android:paddingTop="30dp"
        android:layout_weight="1"
        android:background="@drawable/system_bar_background"
        android:animateLayoutChanges="true">

        <com.android.systemui.statusbar.car.CarNavigationButton
            android:id="@+id/home"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            systemui:intent="intent:#Intent;action=android.intent.action.MAIN;category=android.intent.category.HOME;launchFlags=0x14000000;end"
            android:src="@drawable/car_ic_overview"
            android:background="?android:attr/selectableItemBackground"
            android:paddingTop="30dp"
            android:paddingBottom="30dp"
        />

        <com.android.systemui.statusbar.car.CarNavigationButton
            android:id="@+id/hvac"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            systemui:intent="intent:#Intent;action=android.car.intent.action.TOGGLE_HVAC_CONTROLS;end"
            systemui:broadcast="true"
            android:src="@drawable/car_ic_hvac"
            android:background="?android:attr/selectableItemBackground"
            android:paddingTop="30dp"
            android:paddingBottom="30dp"
        />
    </LinearLayout>
</com.android.systemui.statusbar.car.CarNavigationBarView>
