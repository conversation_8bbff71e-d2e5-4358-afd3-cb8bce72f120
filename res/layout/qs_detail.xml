<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<!-- Extends LinearLayout -->
<com.android.systemui.qs.QSDetail
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/qs_detail_background"
    android:clickable="true"
    android:orientation="vertical"
    android:paddingBottom="8dp"
    android:visibility="invisible"
    android:elevation="4dp" >

    <include
        android:id="@+id/qs_detail_header"
        layout="@layout/qs_detail_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        />

    <com.android.systemui.statusbar.AlphaOptimizedImageView
        android:id="@+id/qs_detail_header_progress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:alpha="0"
        android:background="@color/qs_detail_progress_track"
        android:src="@drawable/indeterminate_anim"
        android:scaleType="fitXY"
        />

    <com.android.systemui.qs.NonInterceptingScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <FrameLayout
            android:id="@android:id/content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </com.android.systemui.qs.NonInterceptingScrollView>

    <include layout="@layout/qs_detail_buttons" />

</com.android.systemui.qs.QSDetail>
