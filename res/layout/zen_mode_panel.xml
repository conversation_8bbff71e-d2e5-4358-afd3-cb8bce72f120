<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<!-- extends LinearLayout -->
<com.android.systemui.volume.ZenModePanel xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/zen_mode_panel"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false" >

    <LinearLayout
        android:id="@+id/edit_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?android:attr/colorPrimary"
        android:clipChildren="false"
        android:orientation="vertical">

        <com.android.systemui.volume.SegmentedButtons
            android:id="@+id/zen_buttons"
            android:background="@drawable/segmented_buttons_background"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="8dp" />

        <RelativeLayout
            android:id="@+id/zen_introduction"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:background="@drawable/zen_introduction_message_background"
            android:theme="@*android:style/ThemeOverlay.DeviceDefault.Accent.Light">

            <ImageView
                android:id="@+id/zen_introduction_confirm"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:layout_alignParentEnd="true"
                android:background="@drawable/btn_borderless_rect"
                android:clickable="true"
                android:contentDescription="@string/accessibility_desc_close"
                android:scaleType="center"
                android:src="@drawable/ic_close_white_rounded" />

            <TextView
                android:id="@+id/zen_introduction_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginStart="24dp"
                android:textDirection="locale"
                android:lineSpacingMultiplier="1.20029"
                android:layout_toStartOf="@id/zen_introduction_confirm"
                android:textAppearance="@style/TextAppearance.QS.Introduction" />

            <TextView
                android:id="@+id/zen_introduction_customize"
                style="@style/QSBorderlessButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="12dp"
                android:layout_below="@id/zen_introduction_message"
                android:clickable="true"
                android:focusable="true"
                android:text="@string/zen_priority_customize_button"
                android:textAppearance="@style/TextAppearance.QS.DetailButton.White" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_below="@id/zen_introduction_message"
                android:layout_alignParentEnd="true" />

        </RelativeLayout>

        <com.android.settingslib.notification.ZenRadioLayout
            android:id="@+id/zen_conditions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="4dp"
            android:layout_marginStart="4dp"
            android:paddingBottom="@dimen/zen_mode_condition_detail_bottom_padding"
            android:orientation="horizontal" >
            <RadioGroup
                android:id="@+id/zen_radio_buttons"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <LinearLayout
                android:id="@+id/zen_radio_buttons_content"
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:orientation="vertical"/>
        </com.android.settingslib.notification.ZenRadioLayout>

        <TextView
            android:id="@+id/zen_alarm_warning"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginEnd="16dp"
            android:textDirection="locale"
            android:lineSpacingMultiplier="1.20029"
            android:textAppearance="@style/TextAppearance.QS.Warning" />
    </LinearLayout>

    <LinearLayout
        android:id="@android:id/empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="?android:attr/colorPrimary"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@android:id/icon"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:alpha="?android:attr/disabledAlpha"
            android:tint="?android:attr/colorForeground" />

        <TextView
            android:id="@android:id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:textAppearance="@style/TextAppearance.QS.DetailEmpty"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/auto_rule"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?android:attr/colorPrimary"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        android:orientation="vertical">

        <TextView
            android:id="@android:id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.QS.DetailItemPrimary"/>

    </LinearLayout>

</com.android.systemui.volume.ZenModePanel>
