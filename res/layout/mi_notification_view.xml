<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/notification_view_width"
    android:layout_height="@dimen/notification_view_height"
    android:background="@drawable/mi_notification_view_bg"
    android:id="@+id/content_background">

    <ImageView
        android:id="@+id/icon_iv"
        android:layout_width="@dimen/notification_view_icon_size"
        android:layout_height="@dimen/notification_view_icon_size"
        android:layout_centerVertical="true"
        android:layout_margin="@dimen/notification_view_icon_margin" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/notification_view_title_margin_left"
        android:layout_marginTop="@dimen/notification_view_title_margin_top"
        android:layout_marginEnd="@dimen/notification_view_title_margin_right"
        android:layout_toEndOf="@id/icon_iv"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/icon_iv"
            android:fontFamily="sans-serif-medium"
            android:textColor="@color/mi_notification_title_color"
            android:singleLine="true"
            android:ellipsize="end"
            android:textSize="@dimen/notification_title_text_size" />

        <TextView
            android:id="@+id/content_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/mi_notification_time_color"
            android:ellipsize="end"
            android:maxLines="2"
            android:textSize="@dimen/notification_time_text_size" />
    </LinearLayout>

    <TextView
        android:id="@+id/time_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/notification_view_time_margin_top"
        android:layout_marginEnd="@dimen/notification_view_time_margin_right"
        android:textColor="@color/mi_notification_time_color"
        android:textSize="@dimen/notification_time_text_size" />

    <Button
        android:id="@+id/btn_extra_operate"
        android:layout_width="@dimen/mi_right_notification_extra_button_width"
        android:layout_height="@dimen/mi_right_notification_extra_button_height"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/mi_right_notification_extra_button_margin_end"
        android:background="@mipmap/mi_right_notification_extra_button_bg"
        android:clickable="true"
        android:textColor="@color/mi_right_notification_extra_button_text_color"
        android:textSize="@dimen/mi_right_notification_extra_button_text_size"
        android:visibility="gone" />

</RelativeLayout>