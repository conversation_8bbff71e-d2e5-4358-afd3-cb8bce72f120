<!--
  ~ Copyright (C) 2016 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<!-- Extends FrameLayout -->
<com.android.systemui.statusbar.NotificationShelf
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/notification_shelf_height"
    android:focusable="true"
    android:clickable="true"
    >

    <com.android.systemui.statusbar.NotificationBackgroundView android:id="@+id/backgroundNormal"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        />
    <com.android.systemui.statusbar.NotificationBackgroundView android:id="@+id/backgroundDimmed"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        />
    <com.android.systemui.statusbar.phone.NotificationIconContainer
        android:id="@+id/content"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingStart="@dimen/shelf_icon_container_padding"
        android:paddingEnd="@dimen/shelf_icon_container_padding"
        android:gravity="center_vertical" />

    <com.android.systemui.statusbar.notification.FakeShadowView
        android:id="@+id/fake_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</com.android.systemui.statusbar.NotificationShelf>
