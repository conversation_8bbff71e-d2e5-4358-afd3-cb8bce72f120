<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2018 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->
<com.android.systemui.statusbar.HeadsUpStatusBarView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:visibility="invisible"
    android:id="@+id/heads_up_status_bar_view"
    android:alpha="0"
>
    <!-- This is a space just used as a layout and it's not actually displaying anything. We're
         repositioning the statusbar icon to the position where this is laid out when showing this
         view. -->
    <Space
        android:id="@+id/icon_placeholder"
        android:layout_width="@dimen/status_bar_icon_drawing_size"
        android:layout_height="@dimen/status_bar_icon_drawing_size"
        android:layout_gravity="center_vertical"
    />
    <TextView
        android:id="@+id/text"
        android:textAppearance="@style/TextAppearance.HeadsUpStatusBarText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:ellipsize="marquee"
        android:fadingEdge="horizontal"
        android:textAlignment="viewStart"
        android:paddingStart="6dp"
        android:layout_weight="1"
        android:layout_gravity="center_vertical"
    />

</com.android.systemui.statusbar.HeadsUpStatusBarView>
