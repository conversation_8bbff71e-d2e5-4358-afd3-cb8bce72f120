<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<Button xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/segmented_button_spacing"
    android:layout_weight="1"
    android:gravity="center"
    android:maxLines="2"
    android:lineSpacingMultiplier="1.05026"
    android:textColor="@color/segmented_button_text_selector"
    android:background="@drawable/btn_borderless_rect"
    android:textAppearance="@style/TextAppearance.QS.SegmentedButton"
    android:minHeight="72dp" />
