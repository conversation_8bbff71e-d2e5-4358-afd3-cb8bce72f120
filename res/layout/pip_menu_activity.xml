<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

  <!-- Menu layout -->
  <FrameLayout
      android:id="@+id/menu_container"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:forceHasOverlappingRendering="false">

      <!-- The margins for this container is calculated in the code depending on whether the
           actions_container is visible. -->
      <FrameLayout
          android:id="@+id/expand_container"
          android:layout_width="match_parent"
          android:layout_height="match_parent">
          <ImageView
              android:id="@+id/expand_button"
              android:layout_width="60dp"
              android:layout_height="60dp"
              android:layout_gravity="center"
              android:contentDescription="@string/pip_phone_expand"
              android:padding="10dp"
              android:src="@drawable/pip_expand"
              android:background="?android:selectableItemBackgroundBorderless" />
      </FrameLayout>

      <FrameLayout
          android:id="@+id/actions_container"
          android:layout_width="match_parent"
          android:layout_height="@dimen/pip_action_size"
          android:layout_gravity="bottom"
          android:visibility="invisible">
          <LinearLayout
              android:id="@+id/actions_group"
              android:layout_width="wrap_content"
              android:layout_height="match_parent"
              android:layout_gravity="center_horizontal"
              android:orientation="horizontal"
              android:divider="@android:color/transparent"
              android:showDividers="middle" />
      </FrameLayout>
  </FrameLayout>

    <ImageView
        android:id="@+id/settings"
        android:layout_width="@dimen/pip_action_size"
        android:layout_height="@dimen/pip_action_size"
        android:layout_gravity="top|start"
        android:padding="@dimen/pip_action_padding"
        android:contentDescription="@string/pip_phone_settings"
        android:src="@drawable/ic_settings"
        android:background="?android:selectableItemBackgroundBorderless" />

    <ImageView
        android:id="@+id/dismiss"
        android:layout_width="@dimen/pip_action_size"
        android:layout_height="@dimen/pip_action_size"
        android:layout_gravity="top|end"
        android:padding="@dimen/pip_action_padding"
        android:contentDescription="@string/pip_phone_close"
        android:src="@drawable/ic_close_white"
        android:background="?android:selectableItemBackgroundBorderless" />

</FrameLayout>
