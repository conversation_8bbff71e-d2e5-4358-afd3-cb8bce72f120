<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/size_50">

    <LinearLayout
        android:id="@+id/time_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/wifi_iv"
            android:layout_width="@dimen/size_33"
            android:layout_height="@dimen/size_33"
            android:layout_marginBottom="@dimen/size_4"
            android:layout_gravity="center_vertical" />

        <TextView
            android:id="@+id/time_tv"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/size_43"
            android:fontFamily="@font/Mitype2018-70"
            android:gravity="left|center_vertical"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/size_9"
            android:textSize="@dimen/text_size_36"
            android:textColor="@android:color/black" />
    </LinearLayout>

    <TextView
        android:id="@+id/toast_prev_tv"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:textSize="@dimen/text_size_31"
        android:textColor="@android:color/black"
        android:alpha="0" />

    <TextView
        android:id="@+id/toast_next_tv"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:textSize="@dimen/text_size_31"
        android:textColor="@android:color/black"
        android:alpha="0" />

</RelativeLayout>
