<?xml version="1.0" encoding="UTF-8"?>
<!--
     Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->


<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:clipChildren="false"
    android:alpha="0"
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:gravity="center">

    <ImageView android:id="@+id/user_avatar"
        android:layout_width="@dimen/car_user_switcher_image_avatar_size"
        android:layout_height="@dimen/car_user_switcher_image_avatar_size"
        android:background="@drawable/car_button_ripple_background_inverse"
        android:gravity="center"/>

    <TextView android:id="@+id/user_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/car_user_switcher_vertical_spacing_between_name_and_avatar"
        android:textSize="@dimen/car_user_switcher_name_text_size"
        android:textColor="@color/car_user_switcher_name_text_color"
        android:ellipsize="end"
        android:singleLine="true"
        android:gravity="center"/>

</LinearLayout>
