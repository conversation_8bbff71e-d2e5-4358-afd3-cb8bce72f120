<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<TextView
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/score"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:textStyle="bold"
        android:textSize="22sp"
        android:gravity="center"
        android:textColor="#FFAAAAAA"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:background="@drawable/scorecard"
        android:elevation="@dimen/hud_z"
        />
