<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2018, The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<com.android.systemui.statusbar.AppOpsInfo
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/app_ops_info"
        android:clickable="true"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="vertical"
        android:paddingStart="@*android:dimen/notification_content_margin_start"
        android:paddingEnd="@*android:dimen/notification_content_margin_end"
        android:background="@color/notification_guts_bg_color"
        android:theme="@*android:style/Theme.DeviceDefault.Light">

    <!-- Package Info -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:layout_marginTop="@*android:dimen/notification_header_padding_top" >
        <ImageView
            android:id="@+id/pkgicon"
            android:layout_width="@dimen/notification_guts_header_height"
            android:layout_height="@dimen/notification_guts_header_height"
            android:layout_centerVertical="true"
            android:layout_marginEnd="3dp" />
        <TextView
            android:id="@+id/pkgname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@*android:style/TextAppearance.Material.Notification.Info"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="2dp"
            android:singleLine="true"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/pkgicon" />
    </RelativeLayout>

    <TextView
        android:id="@+id/prompt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@*android:dimen/notification_header_padding_top"
        style="@style/TextAppearance.NotificationInfo.Secondary" />

    <!-- Settings and Done buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/notification_guts_button_spacing"
        android:layout_marginBottom="@dimen/notification_guts_button_spacing"
        android:gravity="end"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/settings"
            android:text="@string/notification_appops_settings"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/ripple_drawable"
            style="@style/TextAppearance.NotificationInfo.Button"/>
        <TextView
            android:id="@+id/ok"
            android:text="@string/notification_appops_ok"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/ripple_drawable"
            android:minWidth="48dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="-8dp"
            style="@style/TextAppearance.NotificationInfo.Button"/>
    </LinearLayout>
</com.android.systemui.statusbar.AppOpsInfo>
