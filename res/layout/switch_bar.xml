<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/switch_bar"
    android:layout_width="match_parent"
    android:layout_height="?android:attr/actionBarSize"
    android:background="@drawable/switchbar_background"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:clickable="true"
    android:gravity="center"
    android:theme="@*android:style/ThemeOverlay.DeviceDefault.Accent">

    <TextView android:id="@+id/switch_text"
        android:layout_height="wrap_content"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_gravity="center_vertical"
        android:paddingStart="48dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:textAppearance="@android:style/TextAppearance.Material.Title"
        android:textColor="?android:attr/textColorPrimaryInverse"
        android:textAlignment="viewStart"
        android:text="@string/switch_bar_on" />

    <Switch
        android:id="@android:id/switch_widget"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:background="@null" />

</LinearLayout>
