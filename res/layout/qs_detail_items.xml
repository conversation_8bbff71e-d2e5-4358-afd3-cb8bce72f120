<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<!-- extends FrameLayout -->
<com.android.systemui.qs.QSDetailItems
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:sysui="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="@dimen/qs_detail_padding_start"
    android:paddingEnd="16dp">

    <com.android.systemui.qs.AutoSizingList
        android:id="@android:id/list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        sysui:itemHeight="@dimen/qs_detail_item_height"
        style="@style/AutoSizingList"/>

    <LinearLayout
        android:id="@android:id/empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@android:id/icon"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:tint="?android:attr/textColorSecondary" />

        <TextView
            android:id="@android:id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:textAppearance="@style/TextAppearance.QS.DetailEmpty"/>
    </LinearLayout>
</com.android.systemui.qs.QSDetailItems>
