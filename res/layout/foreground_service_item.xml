<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2017, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="?android:attr/listPreferredItemHeight"
    android:paddingStart="?android:attr/listPreferredItemPaddingStart"
    android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/app_icon"
        android:layout_width="@android:dimen/app_icon_size"
        android:layout_height="@android:dimen/app_icon_size"
        android:layout_marginEnd="8dp"
        android:scaleType="centerInside"
        android:contentDescription="@null" />

    <TextView
        android:id="@+id/app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="fill_horizontal|center_vertical"
        android:singleLine="true"
        android:ellipsize="marquee"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textAlignment="viewStart" />

</LinearLayout>
