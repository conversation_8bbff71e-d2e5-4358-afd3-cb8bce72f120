<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->
<com.android.systemui.statusbar.KeyboardShortcutAppItemLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="48dp"
        android:paddingStart="24dp"
        android:paddingEnd="24dp"
        android:paddingBottom="8dp">
    <ImageView
            android:id="@+id/keyboard_shortcuts_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="24dp"
            android:layout_gravity="center_vertical"
            android:visibility="gone"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"/>
    <TextView
            android:id="@+id/keyboard_shortcuts_keyword"
            android:layout_toEndOf="@+id/keyboard_shortcuts_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingEnd="12dp"
            android:paddingBottom="4dp"
            android:textColor="@color/ksh_keyword_color"
            android:textSize="16sp"
            android:maxLines="5"
            android:singleLine="false"
            android:scrollHorizontally="false"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"/>
    <com.android.systemui.statusbar.KeyboardShortcutKeysLayout
            android:id="@+id/keyboard_shortcuts_item_container"
            android:layout_toEndOf="@+id/keyboard_shortcuts_keyword"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:textSize="14sp"
            android:scrollHorizontally="false"
            android:layout_centerVertical="true"
            android:focusable="true"/>
</com.android.systemui.statusbar.KeyboardShortcutAppItemLayout>
