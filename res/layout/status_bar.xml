<?xml version="1.0" encoding="utf-8"?><!--
**
** Copyright 2006, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License atadb re
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!--    android:background="@drawable/status_bar_closed_default_background" -->
<com.android.systemui.statusbar.phone.PhoneStatusBarView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res/com.android.systemui"
    android:id="@+id/status_bar"
    android:layout_width="match_parent"
    android:layout_height="@dimen/status_bar_height"
    android:accessibilityPaneTitle="@string/status_bar"
    android:background="@drawable/system_bar_background"
    android:descendantFocusability="afterDescendants"
    android:focusable="false"
    android:orientation="vertical">

    <!-- XIAOMI 调整下时间显示位置 -->
    <com.android.systemui.statusbar.policy.StatusBarClock
        android:id="@+id/clock"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/status_bar_clock_margin_start"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:textAppearance="@style/TextAppearance.StatusBar.Clock" />

    <com.android.systemui.statusbar.policy.DevicesStatusIconContainer
        android:id="@+id/devices_container"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/mi_devices_status_margin_left"
        android:gravity="center_vertical"
        android:singleLine="true" />

    <ImageView
        android:id="@+id/notification_lights_out"
        android:layout_width="@dimen/status_bar_icon_size"
        android:layout_height="match_parent"
        android:paddingStart="@dimen/status_bar_padding_start"
        android:paddingBottom="2dip"
        android:scaleType="center"
        android:src="@drawable/ic_sysbar_lights_out_dot_small"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/status_bar_contents"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:paddingStart="@dimen/status_bar_padding_start"
        android:paddingEnd="@dimen/status_bar_padding_end">

        <ViewStub
            android:id="@+id/operator_name"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout="@layout/operator_name" />

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <include layout="@layout/heads_up_status_bar_layout" />

            <!-- The alpha of the left side is controlled by PhoneStatusBarTransitions, and the
             individual views are controlled by StatusBarManager disable flags DISABLE_CLOCK and
             DISABLE_NOTIFICATION_ICONS, respectively -->
            <LinearLayout
                android:id="@+id/status_bar_left_side"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false">

                <com.android.systemui.statusbar.AlphaOptimizedFrameLayout
                    android:id="@+id/notification_icon_area"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:clipChildren="false"
                    android:orientation="horizontal" />

            </LinearLayout>
        </FrameLayout>

        <!-- Space should cover the notch (if it exists) and let other views lay out around it -->
        <android.widget.Space
            android:id="@+id/cutout_space_view"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center_horizontal|center_vertical" />

        <com.android.keyguard.AlphaOptimizedLinearLayout
            android:id="@+id/system_icon_area"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical|end"
            android:orientation="horizontal">

            <include layout="@layout/system_icons" />
        </com.android.keyguard.AlphaOptimizedLinearLayout>
    </LinearLayout>

    <ViewStub
        android:id="@+id/emergency_cryptkeeper_text"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout="@layout/emergency_cryptkeeper_text" />

</com.android.systemui.statusbar.phone.PhoneStatusBarView>
