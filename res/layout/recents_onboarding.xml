<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2018 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_height="wrap_content"
    android:layout_width="wrap_content"
    android:paddingBottom="13dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="24dp"
        android:paddingEnd="4dp"
        android:background="@drawable/recents_onboarding_toast_rounded_background"
        android:layout_gravity="center_horizontal"
        android:elevation="2dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/onboarding_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center_vertical"
            android:textColor="@android:color/white"
            android:textSize="16sp"/>
        <ImageView
            android:id="@+id/dismiss"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:padding="10dp"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="2dp"
            android:alpha="0.7"
            android:src="@drawable/ic_close_white"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/accessibility_desc_close"/>
    </LinearLayout>

    <View
        android:id="@+id/arrow"
        android:elevation="2dp"
        android:layout_width="10dp"
        android:layout_height="8dp"
        android:layout_marginTop="-2dp"
        android:layout_gravity="center_horizontal"/>
</LinearLayout>