<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/qs_light_width"
    android:layout_height="@dimen/qs_light_height"
    android:layout_marginTop="@dimen/qs_tile_margin"
    android:clickable="true">

    <com.android.systemui.qs.mi.VerticalSeekBar
        android:id="@+id/volume_slider"
        android:layout_width="@dimen/qs_light_width"
        android:layout_height="@dimen/qs_light_height"
        android:focusable="false"
        android:maxWidth="@dimen/qs_light_width"
        android:maxHeight="@dimen/qs_light_height"
        android:minWidth="@dimen/qs_light_width"
        android:minHeight="@dimen/qs_light_height"
        android:progressDrawable="@drawable/ic_seek_bar_progress_background"
        android:thumb="@null" />

    <ImageView
        android:id="@+id/volume_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/qs_tile_margin"
        android:src="@drawable/menu_voice" />

</RelativeLayout>
