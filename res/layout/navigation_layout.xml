<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.android.systemui.statusbar.phone.NearestTouchFrame
        android:id="@+id/nav_buttons"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingStart="@dimen/rounded_corner_content_padding"
        android:paddingEnd="@dimen/rounded_corner_content_padding"
        android:clipChildren="false"
        android:clipToPadding="false">

        <LinearLayout
            android:id="@+id/ends_group"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingStart="@dimen/nav_content_padding"
            android:paddingEnd="@dimen/nav_content_padding"
            android:clipToPadding="false"
            android:clipChildren="false" />

        <LinearLayout
            android:id="@+id/center_group"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingStart="@dimen/nav_content_padding"
            android:paddingEnd="@dimen/nav_content_padding"
            android:clipToPadding="false"
            android:clipChildren="false" />

    </com.android.systemui.statusbar.phone.NearestTouchFrame>

</FrameLayout>
