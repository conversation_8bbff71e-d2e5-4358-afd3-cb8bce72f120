<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<!-- extends LinearLayout -->
<com.android.systemui.tuner.TunerZenModePanel
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tuner_zen_mode_panel"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:visibility="gone"
    android:orientation="vertical" >

    <View
        android:id="@+id/zen_embedded_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="12dp"
        android:layout_marginTop="8dp"
        android:background="@color/qs_tile_divider" />

    <include
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginStart="16dp"
        android:id="@+id/tuner_zen_switch"
        layout="@layout/qs_detail_header" />

    <include layout="@layout/zen_mode_panel" />

    <include
        android:id="@+id/tuner_zen_buttons"
        layout="@layout/qs_detail_buttons" />

</com.android.systemui.tuner.TunerZenModePanel>
