<!--
  ~ Copyright (C) 2014 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<!-- Extends Framelayout -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:visibility="gone">

    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_weight="1.0" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/notification_clear_all_end_margin"
        android:orientation="vertical"
        android:layout_gravity="center_vertical|end">

        <ImageView
            android:id="@+id/fold_notification"
            android:layout_width="@dimen/status_bar_fold_clear_icon_size"
            android:layout_height="@dimen/status_bar_fold_clear_icon_size"
            android:layout_marginBottom="@dimen/status_bar_fold_clear_icon_margin"
            android:layout_gravity="end"
            android:focusable="true"
            android:background="@drawable/fold_mi_notification"
            android:visibility="invisible"/>

        <ImageView
            android:id="@+id/dismiss_view"
            android:layout_width="@dimen/status_bar_fold_clear_icon_size"
            android:layout_height="@dimen/status_bar_fold_clear_icon_size"
            android:layout_gravity="end"
            android:focusable="true"
            android:background="@drawable/clear_mi_notification"/>
    </LinearLayout>

</LinearLayout>
