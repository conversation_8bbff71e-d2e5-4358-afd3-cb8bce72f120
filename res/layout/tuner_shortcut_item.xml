<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:paddingStart="4dp"
    android:paddingEnd="4dp"
    android:clickable="true"
    android:gravity="center"
    android:background="?android:attr/selectableItemBackground">

    <ImageView
        android:id="@android:id/icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:padding="12dp" />

    <TextView android:id="@android:id/title"
        android:layout_height="wrap_content"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_gravity="center_vertical"
        android:textAppearance="?android:attr/textAppearanceListItem"
        android:textColor="?android:attr/textColorPrimary" />

    <com.android.systemui.statusbar.phone.ExpandableIndicator
        android:id="@+id/expand"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:padding="12dp"
        android:visibility="gone" />

</LinearLayout>
