<?xml version="1.0" encoding="utf-8"?>
<!--
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
-->

<!-- Extends FrameLayout -->
<com.android.systemui.qs.QSFooterImpl
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/qs_footer"
    android:layout_width="match_parent"
    android:layout_height="@dimen/qs_footer_height"
    android:elevation="4dp"
    android:background="@android:color/transparent"
    android:baselineAligned="false"
    android:clickable="false"
    android:clipChildren="false"
    android:clipToPadding="false">

    <View
        android:id="@+id/qs_footer_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_gravity="top"
        android:background="?android:attr/dividerHorizontal"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:gravity="end" >

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical|start"
            android:paddingStart="16dp">

            <include
                layout="@layout/mobile_signal_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:visibility="gone" />

            <com.android.keyguard.CarrierText
                android:id="@+id/qs_carrier_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="32dp"
                android:ellipsize="marquee"
                android:textAppearance="@style/TextAppearance.QS.TileLabel"
                android:textColor="?android:attr/textColorPrimary"
                android:textDirection="locale"
                android:singleLine="true" />

        </LinearLayout>

        <com.android.systemui.qs.PageIndicator
            android:id="@+id/footer_page_indicator"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:visibility="gone" />

        <com.android.keyguard.AlphaOptimizedLinearLayout
            android:id="@+id/qs_footer_actions_container"
            android:layout_width="@integer/qs_footer_actions_width"
            android:layout_height="match_parent"
            android:layout_weight="@integer/qs_footer_actions_weight"
            android:gravity="center_vertical|end" >
            <com.android.systemui.statusbar.phone.MultiUserSwitch
                android:id="@+id/multi_user_switch"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_alignParentEnd="true"
                android:background="@drawable/ripple_drawable"
                android:focusable="true">

                <ImageView
                    android:id="@+id/multi_user_avatar"
                    android:layout_width="@dimen/multi_user_avatar_expanded_size"
                    android:layout_height="@dimen/multi_user_avatar_expanded_size"
                    android:layout_gravity="center"
                    android:scaleType="centerInside"/>
            </com.android.systemui.statusbar.phone.MultiUserSwitch>

            <com.android.systemui.statusbar.AlphaOptimizedImageView
                android:id="@android:id/edit"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:clipToPadding="false"
                android:contentDescription="@string/accessibility_quick_settings_edit"
                android:focusable="true"
                android:padding="15dp"
                android:src="@drawable/ic_mode_edit"
                android:tint="?android:attr/colorForeground"/>

            <com.android.systemui.statusbar.AlphaOptimizedFrameLayout
                android:id="@+id/settings_button_container"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:clipChildren="false"
                android:clipToPadding="false">

                <com.android.systemui.statusbar.phone.SettingsButton
                    android:id="@+id/settings_button"
                    style="@android:style/Widget.Material.Button.Borderless"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/ripple_drawable"
                    android:contentDescription="@string/accessibility_quick_settings_settings"
                    android:padding="15dp"
                    android:src="@drawable/ic_settings_16dp"
                    android:tint="?android:attr/colorForeground"/>

                <com.android.systemui.statusbar.AlphaOptimizedImageView
                    android:id="@+id/tuner_icon"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingStart="36dp"
                    android:paddingEnd="4dp"
                    android:src="@drawable/tuner"
                    android:tint="?android:attr/textColorTertiary"
                    android:visibility="invisible"/>

            </com.android.systemui.statusbar.AlphaOptimizedFrameLayout>
        </com.android.keyguard.AlphaOptimizedLinearLayout>
    </LinearLayout>

    <View
        android:id="@+id/qs_drag_handle_view"
        android:layout_width="24dp"
        android:layout_height="4dp"
        android:layout_marginBottom="16dp"
        android:layout_gravity="center_horizontal|bottom"
        android:background="@drawable/qs_footer_drag_handle" />

</com.android.systemui.qs.QSFooterImpl>
