<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:layout_marginStart="1dp"
    android:layout_marginEnd="0dp"
    android:layout_weight="1"
    android:gravity="center_vertical" >

    <LinearLayout
        android:id="@android:id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="48dp"
        android:gravity="center_vertical"
        android:layout_centerVertical="true"
        android:orientation="vertical"
        android:layout_toEndOf="@android:id/checkbox"
        android:layout_toStartOf="@android:id/button1">

        <TextView
            android:id="@android:id/text1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:textAlignment="viewStart"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.QS.DetailItemPrimary" />

        <TextView
            android:id="@android:id/text2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/zen_mode_condition_detail_item_interline_spacing"
            android:ellipsize="end"
            android:textAlignment="viewStart"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.QS.DetailItemSecondary" />

    </LinearLayout>

    <ImageView
        android:id="@android:id/button1"
        style="@style/QSBorderlessButton"
        android:background="@drawable/ripple_drawable_20dp"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_centerVertical="true"
        android:scaleType="center"
        android:layout_toStartOf="@android:id/button2"
        android:contentDescription="@string/accessibility_quick_settings_less_time"
        android:tint="?android:attr/textColorPrimary"
        android:src="@drawable/ic_qs_minus" />

    <ImageView
        android:id="@android:id/button2"
        style="@style/QSBorderlessButton"
        android:background="@drawable/ripple_drawable_20dp"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentEnd="true"
        android:scaleType="center"
        android:layout_centerVertical="true"
        android:contentDescription="@string/accessibility_quick_settings_more_time"
        android:tint="?android:attr/textColorPrimary"
        android:src="@drawable/ic_qs_plus" />

</RelativeLayout>
