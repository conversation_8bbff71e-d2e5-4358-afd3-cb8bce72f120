<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<!-- The layouts params are calculated in TaskViewHeader.java -->
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.android.systemui.recents.views.FixedSizeImageView
        android:id="@+id/app_icon"
        android:contentDescription="@string/recents_app_info_button_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|start"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:paddingStart="16dp"
        android:paddingEnd="12dp" />
    <TextView
        android:id="@+id/app_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|start"
        android:textSize="16sp"
        android:textColor="#ffffffff"
        android:text="@string/recents_empty_message"
        android:fontFamily="sans-serif-medium"
        android:singleLine="true"
        android:maxLines="2"
        android:ellipsize="marquee"
        android:fadingEdge="horizontal" />
    <com.android.systemui.recents.views.FixedSizeImageView
        android:id="@+id/app_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|end"
        android:padding="@dimen/recents_task_view_header_button_padding"
        android:background="?android:selectableItemBackground"
        android:src="@drawable/recents_info_light" />
</FrameLayout>