<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2014, The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<!-- extends FrameLayout -->
<com.android.systemui.statusbar.ExpandableNotificationRow
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:focusable="true"
    android:clickable="true"
    >

    <!-- Menu displayed behind notification added here programmatically -->

    <com.android.systemui.statusbar.NotificationBackgroundView
        android:id="@+id/backgroundNormal"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.android.systemui.statusbar.NotificationBackgroundView
        android:id="@+id/backgroundDimmed"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.android.systemui.statusbar.NotificationContentView android:id="@+id/expanded"
        android:background="@android:color/transparent"
       android:layout_width="match_parent"
       android:layout_height="wrap_content" />

    <com.android.systemui.statusbar.NotificationContentView android:id="@+id/expandedPublic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <Button
        android:id="@+id/veto"
        android:layout_width="48dp"
        android:layout_height="0dp"
        android:gravity="end"
        android:layout_marginEnd="-80dp"
        android:background="@null"
        android:paddingEnd="8dp"
        android:paddingStart="8dp"
        />

    <ViewStub
        android:layout="@layout/notification_children_container"
        android:id="@+id/child_container_stub"
        android:inflatedId="@+id/notification_children_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        />

    <ViewStub
        android:layout="@layout/notification_guts"
        android:id="@+id/notification_guts_stub"
        android:inflatedId="@+id/notification_guts"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        />

    <com.android.systemui.statusbar.notification.FakeShadowView
        android:id="@+id/fake_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</com.android.systemui.statusbar.ExpandableNotificationRow>
