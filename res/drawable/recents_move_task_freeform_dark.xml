<!--
Copyright (C) 2015 The Android Open Source Project

   Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">

    <group
            android:translateX="-286.000000"
            android:translateY="-602.000000">
        <group
                android:translateX="109.000000"
                android:translateY="514.000000">
            <group
                    android:translateX="178.000000"
                    android:translateY="89.000000">
                <path
                    android:strokeColor="@color/recents_task_bar_dark_icon_color"
                    android:strokeWidth="2"
                    android:pathData="M10,12 L10,3 L19,3 L19,5 L19,11 L19,12 L10,12 Z" />
                <path
                    android:strokeColor="@color/recents_task_bar_dark_icon_color"
                    android:strokeWidth="2"
                    android:pathData="M15,17 L5,17 L5,7 L5,17 Z" />
            </group>
        </group>
    </group>
</vector>