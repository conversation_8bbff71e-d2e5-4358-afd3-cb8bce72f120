<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2017 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<animated-vector
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:drawable="@drawable/lockscreen_fingerprint_error_state_to_fp" >
    <target
        android:name="fingerprintwhite"
        android:animation="@anim/ic_fingerprint_tofp_fingerprintwhite_animation" />
    <target
        android:name="ridge_5_path"
        android:animation="@anim/ic_fingerprint_tofp_ridge_5_path_animation" />
    <target
        android:name="ridge_7_path"
        android:animation="@anim/ic_fingerprint_tofp_ridge_7_path_animation" />
    <target
        android:name="ridge_6_path"
        android:animation="@anim/ic_fingerprint_tofp_ridge_6_path_animation" />
    <target
        android:name="ridge_2_path"
        android:animation="@anim/ic_fingerprint_tofp_ridge_2_path_animation" />
    <target
        android:name="ridge_1_path"
        android:animation="@anim/ic_fingerprint_tofp_ridge_1_path_animation" />
    <target
        android:name="errorcircle"
        android:animation="@anim/ic_fingerprint_tofp_errorcircle_animation" />
    <target
        android:name="circlepath"
        android:animation="@anim/ic_fingerprint_tofp_circlepath_animation" />
    <target
        android:name="errorexclamation"
        android:animation="@anim/ic_fingerprint_tofp_errorexclamation_animation" />
    <target
        android:name="exclamationbottom"
        android:animation="@anim/ic_fingerprint_tofp_exclamationbottom_animation" />
    <target
        android:name="bottompath"
        android:animation="@anim/ic_fingerprint_tofp_bottompath_animation" />
    <target
        android:name="exclamationtop"
        android:animation="@anim/ic_fingerprint_tofp_exclamationtop_animation" />
    <target
        android:name="toppath"
        android:animation="@anim/ic_fingerprint_tofp_toppath_animation" />
</animated-vector>
