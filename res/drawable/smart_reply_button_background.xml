<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (C) 2017 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<ripple xmlns:android="http://schemas.android.com/apk/res/android"
        android:color="@color/notification_ripple_untinted_color">
    <item>
        <inset
            android:insetLeft="0dp"
            android:insetTop="8dp"
            android:insetRight="0dp"
            android:insetBottom="8dp">
            <shape android:shape="rectangle">
                <corners android:radius="8dp" />
                <stroke android:width="@dimen/smart_reply_button_stroke_width"
                        android:color="@color/smart_reply_button_stroke" />
                <solid android:color="@color/smart_reply_button_background"/>
            </shape>
        </inset>
    </item>
</ripple>
