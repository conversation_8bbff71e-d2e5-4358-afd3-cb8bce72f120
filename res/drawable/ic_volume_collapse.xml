<!--
     Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<vector
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:name="ic_caret_down"
    android:width="24dp"
    android:viewportWidth="24"
    android:height="24dp"
    android:viewportHeight="24"
    android:tint="?android:attr/colorControlNormal" >
    <group
        android:name="caret___4" >
        <group
            android:name="right"
            android:translateX="11.287"
            android:translateY="8.701"
            android:rotation="45" >
            <group
                android:name="right_pivot"
                android:translateX="4.242" >
                <path
                    android:name="rectangle_path_1"
                    android:fillColor="#FFFFFFFF"
                    android:pathData="M -3.242,-1.0 l 6.484,0.0 c 0.**********,0.0 1.0,0.********** 1.0,1.0 l 0.0,0.0 c 0.0,0.********** -0.**********,1.0 -1.0,1.0 l -6.484,0.0 c -0.**********,0.0 -1.0,-0.********** -1.0,-1.0 l 0.0,0.0 c 0.0,-0.********** 0.**********,-1.0 1.0,-1.0 Z" />
            </group>
        </group>
        <group
            android:name="left"
            android:translateX="12.699"
            android:translateY="8.701"
            android:rotation="-45" >
            <group
                android:name="left_pivot"
                android:translateX="-4.242" >
                <path
                    android:name="rectangle_path_2"
                    android:fillColor="#FFFFFFFF"
                    android:pathData="M -3.242,-1.0 l 6.484,0.0 c 0.**********,0.0 1.0,0.********** 1.0,1.0 l 0.0,0.0 c 0.0,0.********** -0.**********,1.0 -1.0,1.0 l -6.484,0.0 c -0.**********,0.0 -1.0,-0.********** -1.0,-1.0 l 0.0,0.0 c 0.0,-0.********** 0.**********,-1.0 1.0,-1.0 Z" />
            </group>
        </group>
    </group>
</vector>
