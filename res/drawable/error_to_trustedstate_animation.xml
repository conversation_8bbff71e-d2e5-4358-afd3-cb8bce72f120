<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2017 The Android Open Source Project

   Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<animated-vector
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:drawable="@drawable/error_to_trustedstate" >
    <target
        android:name="ellipse_path_1"
        android:animation="@anim/error_to_trustedstate_ellipse_path_1_animation" />
    <target
        android:name="path_1"
        android:animation="@anim/error_to_trustedstate_path_1_animation" />
    <target
        android:name="path_2"
        android:animation="@anim/error_to_trustedstate_path_2_animation" />
    <target
        android:name="lock_top"
        android:animation="@anim/error_to_trustedstate_lock_top_animation" />
    <target
        android:name="path_3"
        android:animation="@anim/error_to_trustedstate_path_3_animation" />
    <target
        android:name="errorexclamationdot"
        android:animation="@anim/error_to_trustedstate_errorexclamationdot_animation" />
    <target
        android:name="bottompath"
        android:animation="@anim/error_to_trustedstate_bottompath_animation" />
    <target
        android:name="exclamationtop"
        android:animation="@anim/error_to_trustedstate_exclamationtop_animation" />
    <target
        android:name="toppath"
        android:animation="@anim/error_to_trustedstate_toppath_animation" />
    <target
        android:name="errorcircle"
        android:animation="@anim/error_to_trustedstate_errorcircle_animation" />
    <target
        android:name="circlepath"
        android:animation="@anim/error_to_trustedstate_circlepath_animation" />
</animated-vector>
