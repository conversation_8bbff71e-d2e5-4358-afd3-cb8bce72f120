<!--
 Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:drawable="@drawable/indeterminate" >

    <target
        android:name="path1"
        android:animation="@anim/progress_indeterminate_horizontal_rect1_translate" />
    <target
        android:name="path1"
        android:animation="@anim/progress_indeterminate_horizontal_rect1_scale" />

    <target
        android:name="path2"
        android:animation="@anim/progress_indeterminate_horizontal_rect2_translate" />
    <target
        android:name="path2"
        android:animation="@anim/progress_indeterminate_horizontal_rect2_scale" />
</animated-vector>
