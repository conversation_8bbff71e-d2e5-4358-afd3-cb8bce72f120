<?xml version="1.0" encoding="utf-8"?>
<animated-vector
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:drawable="@drawable/ic_signal_workmode_enable" >
    <target
        android:name="mask_1"
        android:animation="@anim/ic_signal_workmode_enable_mask_1_animation" />
    <target
        android:name="whole"
        android:animation="@anim/ic_signal_workmode_enable_whole_animation" />
    <target
        android:name="rectangle_path_3_position"
        android:animation="@anim/ic_signal_workmode_enable_rectangle_path_3_position_animation" />
    <target
        android:name="rectangle_path_3"
        android:animation="@anim/ic_signal_workmode_enable_rectangle_path_3_animation" />
    <target
        android:name="rectangle_path_4_position"
        android:animation="@anim/ic_signal_workmode_enable_rectangle_path_4_position_animation" />
    <target
        android:name="rectangle_path_4"
        android:animation="@anim/ic_signal_workmode_enable_rectangle_path_4_animation" />
    <target
        android:name="left"
        android:animation="@anim/ic_signal_workmode_enable_left_animation" />
    <target
        android:name="right"
        android:animation="@anim/ic_signal_workmode_enable_right_animation" />
    <target
        android:name="stick"
        android:animation="@anim/ic_signal_workmode_enable_stick_animation" />
    <target
        android:name="ic_signal_workmode_enable"
        android:animation="@anim/ic_signal_workmode_enable_stickito_animation" />
</animated-vector>
