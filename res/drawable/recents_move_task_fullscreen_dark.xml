<!--
Copyright (C) 2015 The Android Open Source Project

   Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    <group
            android:translateX="-252.000000"
            android:translateY="-602.000000">
        <group
                android:translateX="109.000000"
                android:translateY="514.000000">
            <group
                    android:translateX="144.000000"
                    android:translateY="89.000000">
                <path
                    android:strokeColor="@color/recents_task_bar_dark_icon_color"
                    android:strokeWidth="2"
                    android:pathData="M17,17 L5,17 L5,5 L17,5 L17,17 Z" />
            </group>
        </group>
    </group>
</vector>