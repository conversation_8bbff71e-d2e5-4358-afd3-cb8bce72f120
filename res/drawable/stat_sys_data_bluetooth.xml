<?xml version="1.0" encoding="utf-8"?><!--
**
** Copyright 2017, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="16dp"
    android:height="18dp"
    android:viewportWidth="16"
    android:viewportHeight="18">

    <group
        android:translateX="-1134.000000"
        android:translateY="-40.000000">
        <path
            android:fillColor="#272727"
            android:pathData="M1141.54162,40 C1141.77164,40 1141.98857,40.0560516 1142.1795,40.1552412
C1142.22265,40.1609831 1142.26484,40.1754746 1142.30453,40.1978171
L1148.81905,43.8634995 C1149.14987,43.9733807 1149.45233,44.1778994
1149.68257,44.4693765 C1150.18523,45.1057387 1150.07685,46.0291025
1149.44049,46.5317673 C1149.39709,46.5660438 1149.3518,46.5978417
1149.30482,46.6270089 L1145.804,48.8 L1149.30482,50.973073
C1149.32831,50.9876566 1149.35138,51.0028979 1149.374,51.0187778
L1149.44049,51.0683146 C1150.07685,51.5709793 1150.18523,52.4943432
1149.68257,53.1307054 C1149.55091,53.2973795 1149.39564,53.4356196
1149.22495,53.5439897 L1149.22426,53.5443357 L1142.24298,57.8418611
C1142.236,57.8350606 1142.22907,57.8282139 1142.22218,57.8213213
C1142.02199,57.9349601 1141.7894,58 1141.54162,58 C1140.7764,58
1140.15606,57.3796623 1140.15606,56.6144369 L1140.156,49.138
L1134.69819,45.7500296 C1134.6747,45.735446 1134.65163,45.7202047
1134.62901,45.7043248 L1134.56252,45.654788 C1133.92616,45.1521232
1133.81777,44.2287594 1134.32044,43.5923972 C1134.90103,42.8573809
1135.94088,42.6753279 1136.73665,43.1693765 L1136.73665,43.1693765
L1140.156,45.292 L1140.15606,41.3855631 C1140.15606,40.6203377 1140.7764,40
1141.54162,40 Z M1138.47866,50.0730932 C1138.55199,50.1895218
1138.59231,50.3236865 1138.5953,50.4612501 L1138.63581,52.3210506
C1138.64135,52.5753969 1138.51915,52.8156188 1138.31032,52.9609206
L1136.69752,54.0830915 C1135.93692,54.6123102 1134.89585,54.4584094
1134.3209,53.7317572 C1133.81345,53.0904079 1133.92199,52.1591194
1134.56334,51.651666 C1134.60489,51.618791 1134.64817,51.5881697
1134.69301,51.559934 L1137.43142,49.835232 C1137.78629,49.6117267
1138.25516,49.7182208 1138.47866,50.0730932 Z M1142.927,50.859 L1142.927,54.271
L1145.599,52.518 L1142.927,50.859 Z M1142.927,43.484 L1142.927,46.74
L1145.586,45.089 L1142.927,43.484 Z"
            android:strokeWidth="1" />
    </group>
</vector>
