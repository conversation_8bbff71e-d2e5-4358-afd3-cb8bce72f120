<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2015 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->
<animated-vector
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:drawable="@drawable/lockscreen_fingerprint_draw_off" >
    <target
        android:name="ridge_5_path"
        android:animation="@anim/lockscreen_fingerprint_draw_off_ridge_5_path_animation" />
    <target
        android:name="ridge_7_path"
        android:animation="@anim/lockscreen_fingerprint_draw_off_ridge_7_path_animation" />
    <target
        android:name="ridge_6_path"
        android:animation="@anim/lockscreen_fingerprint_draw_off_ridge_6_path_animation" />
    <target
        android:name="ridge_2_path"
        android:animation="@anim/lockscreen_fingerprint_draw_off_ridge_2_path_animation" />
    <target
        android:name="ridge_1_path"
        android:animation="@anim/lockscreen_fingerprint_draw_off_ridge_1_path_animation" />
</animated-vector>
