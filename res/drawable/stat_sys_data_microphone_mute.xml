<?xml version="1.0" encoding="utf-8"?><!--
**
** Copyright 2017, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="19dp"
    android:height="23dp"
    android:viewportWidth="19"
    android:viewportHeight="23">

    <group
        android:translateX="-1072.000000"
        android:translateY="-36.000000">
        <path
            android:fillColor="@color/status_bar_remind_icon_view_color"
            android:pathData="M1081.933,54.242 L1081.93508,56.4912109 C1081.93508,56.5785841
1081.89966,56.6576857 1081.84241,56.7149439 C1081.78515,56.7722022
1081.70605,56.8076172 1081.61867,56.8076172 C1081.5313,56.8076172
1081.4522,56.7722022 1081.39494,56.7149439 C1081.33768,56.6576857
1081.30227,56.5785841 1081.30227,56.4905514 L1081.30227,56.4905514
L1081.30164,55.1827359 L1081.933,54.242 Z M1088.20067,47.7251715
C1088.52525,47.7201975 1088.59181,47.7757077 1088.63142,47.8463307
C1088.67104,47.9169538 1088.68371,48.0026897 1088.66006,48.086799
C1088.28087,49.4348911 1087.54608,50.614972 1086.57197,51.5293965
C1085.47863,52.555732 1084.08377,53.2477085 1082.5521,53.4649573
L1082.447,53.477 L1082.89069,52.8172473 C1083.94964,52.5426016
1084.89323,52.0712746 1085.68411,51.448198 L1085.94203,51.2349471
C1086.94962,50.3608703 1087.68245,49.2253287 1088.05088,47.915454
C1088.07454,47.8313447 1088.13005,47.7647872 1088.20067,47.7251715 Z
M1081.63186,38.1923828 C1082.3393,38.1923828 1082.96818,38.4513601
1083.41867,38.8864728 C1083.85844,39.3112319 1084.12356,39.9009071
1084.12356,40.5515748 L1084.12356,40.5515748 L1084.12158,44.5844606
L1088.44261,40.2589355 L1074.58361,54.7389251 L1076.84512,51.8713683
C1075.67668,50.9180746 1074.79463,49.6095482 1074.36631,48.0866425
C1075.38784,49.3666245 1076.20664,50.5396596 1077.29385,51.4268719
L1077.29385,51.4268719 L1080.25969,48.4552739 C1079.65912,48.0282526
1079.27199,47.3280079 1079.27199,46.5375977 L1079.27199,46.5375977
L1079.27199,40.5522461 C1079.27199,39.9005878 1079.53613,39.310622
1079.96318,38.8835708 C1080.39023,38.4565195 1080.9802,38.1923828
1081.63186,38.1923828 Z"
            android:strokeWidth="2.7421875"
            android:strokeColor="@color/status_bar_remind_icon_view_color" />
    </group>
</vector>
