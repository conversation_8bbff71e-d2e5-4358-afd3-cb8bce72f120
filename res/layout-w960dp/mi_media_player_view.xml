<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/media_relative_layout"
    android:layout_width="@dimen/qs_media_player_view_width"
    android:layout_height="@dimen/qs_media_player_view_height"
    android:background="@drawable/ic_qs_rectangle"
    android:gravity="center_vertical">

    <android.support.v7.widget.CardView
        android:id="@+id/music_guide_card"
        android:layout_width="@dimen/mi_media_cover_size"
        android:layout_height="@dimen/mi_media_cover_size"
        android:layout_marginStart="@dimen/mi_media_cover_left_margin"
        android:visibility="visible"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="@dimen/mi_media_cover_radius"
        app:cardElevation="0dp">

        <ImageView
            android:id="@+id/music_guide"
            android:layout_width="@dimen/mi_media_cover_size"
            android:layout_height="@dimen/mi_media_cover_size"
            android:layout_gravity="center_vertical"
            android:src="@drawable/media_default_icon" />
    </android.support.v7.widget.CardView>

    <LinearLayout
        android:id="@+id/marquee_linear_layout"
        android:layout_width="@dimen/mi_media_tile_width"
        android:layout_height="@dimen/mi_media_tile_height"
        android:layout_below="@id/music_guide_card"
        android:layout_marginTop="@dimen/qs_media_title_text_margin_top"
        android:gravity="center"
        android:layout_centerHorizontal="true"
        android:orientation="vertical">

        <com.android.systemui.qs.mi.MarqueeTextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:text="@string/qs_player_title_default"
            android:textColor="@color/color_white"
            android:textSize="@dimen/text_size_21" />

        <com.android.systemui.qs.mi.MarqueeTextView
            android:id="@+id/artist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/qs_media_desc_padding_top"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:textColor="@color/color_white_40"
            android:textSize="@dimen/text_size_18"
            android:visibility="gone" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/marquee_linear_layout"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/mi_media_next_play_btn_top_margin">

        <ImageView
            android:id="@+id/prev_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/mi_media_next_play_btn_right_margin"
            android:layout_marginTop="@dimen/mi_play_button_margin_linear_top"
            android:src="@drawable/menu_prev_btn" />

        <ImageView
            android:id="@+id/play_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginLeft="@dimen/mi_media_next_play_btn_left_margin"
            android:layout_toRightOf="@id/prev_btn"
            android:layout_marginTop="@dimen/mi_play_button_margin_linear_top"
            android:src="@drawable/menu_pause_btn" />

        <ImageView
            android:id="@+id/next_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/mi_media_next_play_btn_left_margin"
            android:layout_marginTop="@dimen/mi_play_button_margin_linear_top"
            android:src="@drawable/menu_next_btn" />

    </LinearLayout>

</RelativeLayout>
