<?xml version="1.0" encoding="utf-8"?><!--
** Copyright 2017, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res-auto"
    android:id="@+id/quick_status_bar_system_icons"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/quick_status_bar_system_icons_margin_left"
    android:layout_marginTop="@dimen/qs_panel_top_margin"
    android:orientation="horizontal"
    android:paddingStart="@dimen/status_bar_padding_start"
    android:paddingEnd="@dimen/status_bar_padding_end">

    <com.android.systemui.statusbar.policy.Clock
        android:id="@+id/clock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="@color/notification_panel_head_clock_color"
        android:textSize="@dimen/mi_quick_status_bar_system_icons_date_clock_text_size"
        systemui:showDark="false" />

    <com.android.systemui.statusbar.policy.DateView
        android:id="@+id/date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/quick_status_bar_system_icons_date_margin_left"
        android:fontFamily="sans-serif-condensed"
        android:singleLine="true"
        android:textColor="@color/notification_panel_head_date_color"
        android:textSize="@dimen/mi_quick_status_bar_system_icons_date_date_text_size"
        systemui:datePattern="@string/abbrev_wday_month_day_no_year_alarm" />

    <android.widget.Space
        android:id="@+id/space"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_weight="1"
        android:gravity="center_vertical|center_horizontal" />
</LinearLayout>
