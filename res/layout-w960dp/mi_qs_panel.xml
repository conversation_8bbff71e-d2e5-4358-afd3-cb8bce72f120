<?xml version="1.0" encoding="utf-8"?><!--控制中心的布局-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/quick_settings_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/qs_panel_tile_top_margin"
    android:orientation="horizontal">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.android.systemui.qs.PagedTileLayout
            android:id="@+id/tile_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <com.android.systemui.qs.tileimpl.LargeWifiView
            android:id="@+id/qs_large_wifi_tile"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/mi_media_left_margin" />

        <com.android.systemui.qs.mi.MediaPlayerView
            android:id="@+id/qs_media_player_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/mi_media_left_margin"
            android:layout_marginTop="@dimen/mi_media_play_view_margin_top"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/qs_tile_brightness_view_margin_left">

            <include
                android:id="@+id/brightness_view"
                layout="@layout/quick_settings_brightness_dialog"
                android:layout_width="0dp"
                android:layout_height="match_parent" />

            <com.android.systemui.qs.mi.BrightnessView
                android:id="@+id/qs_brightness_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/qs_tile_ui_margin" />

            <FrameLayout
                android:id="@+id/qs_volume_view_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/qs_tile_ui_margin">

                <com.android.systemui.qs.mi.VolumeViewShort
                    android:id="@+id/qs_volume_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top" />

                <ImageView
                    android:id="@+id/qs_more_volume"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/qs_light_more_top_margin"
                    android:src="@drawable/menu_more" />

            </FrameLayout>
        </LinearLayout>
    </FrameLayout>

</LinearLayout>
