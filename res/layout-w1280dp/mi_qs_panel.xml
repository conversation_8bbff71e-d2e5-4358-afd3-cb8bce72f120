<?xml version="1.0" encoding="utf-8"?>
<!--控制中心的布局-->
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/quick_settings_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/qs_panel_tile_top_margin"
    android:orientation="vertical">

        <com.android.systemui.qs.PagedTileLayout
            android:id="@+id/tile_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/qs_panel_left_margin"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <com.android.systemui.qs.tileimpl.LargeWifiView
            android:id="@+id/qs_large_wifi_tile"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/mi_media_left_margin"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <com.android.systemui.qs.mi.MediaPlayerView
            android:id="@+id/qs_media_player_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/mi_media_tile_margin_top"
            android:layout_marginStart="@dimen/mi_media_left_margin"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <include
            android:id="@+id/brightness_view"
            layout="@layout/quick_settings_brightness_dialog"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"/>

        <com.android.systemui.qs.mi.BrightnessView
            android:id="@+id/qs_brightness_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/qs_tile_brightness_view_margin_left2"
            android:layout_marginTop="@dimen/qs_tile_brightness_view_margin_top"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <com.android.systemui.qs.mi.VolumeViewShort
            android:id="@+id/qs_volume_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/qs_tile_margin"
            app:layout_constraintStart_toEndOf="@id/qs_brightness_view"
            app:layout_constraintTop_toTopOf="@id/qs_brightness_view"/>

        <ImageView
            android:id="@+id/qs_more_volume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/qs_light_more_top_margin"
            android:src="@drawable/menu_more"
            app:layout_constraintTop_toTopOf="@id/qs_volume_view"
            app:layout_constraintStart_toStartOf="@id/qs_volume_view"
            app:layout_constraintEnd_toEndOf="@id/qs_volume_view"/>

</android.support.constraint.ConstraintLayout>
