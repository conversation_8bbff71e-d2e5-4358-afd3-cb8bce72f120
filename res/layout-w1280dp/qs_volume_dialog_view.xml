<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/volume_container">

    <RelativeLayout
        android:layout_width="@dimen/qs_light_width"
        android:layout_height="324dp"
        android:layout_alignParentRight="true"
        android:layout_marginTop="251dp"
        android:layout_marginRight="@dimen/size_55">

        <com.android.systemui.qs.mi.VerticalSeekBar
            android:id="@+id/volume_slider"
            android:layout_width="@dimen/qs_light_width"
            android:layout_height="@dimen/qs_volume_dialog_height"
            android:minWidth="@dimen/qs_light_width"
            android:maxWidth="@dimen/qs_light_width"
            android:minHeight="@dimen/qs_volume_dialog_height"
            android:maxHeight="@dimen/qs_volume_dialog_height"
            android:progressDrawable="@drawable/ic_seek_bar_progress_background"
            android:thumb="@null"
            android:focusable="false" />

        <ImageView
            android:id="@+id/volume_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/menu_voice"
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="@dimen/qs_tile_margin_volume" />

        <ImageView
            android:id="@+id/qs_more_volume"
            android:layout_width="match_parent"
            android:layout_marginTop="276dp"
            android:background="@drawable/mi_more_volume_view_bg"
            android:layout_height="48dp"
            android:src="@drawable/menu_more" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="@dimen/qs_light_width"
        android:layout_height="@dimen/qs_volume_dialog_height"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="@dimen/size_193"
        android:visibility="gone"
        android:id="@+id/volume_alarm_slider_all">

        <com.android.systemui.qs.mi.VerticalSeekBar
            android:id="@+id/volume_alarm_slider"
            android:layout_width="@dimen/qs_light_width"
            android:layout_height="@dimen/qs_volume_dialog_height"
            android:minWidth="@dimen/qs_light_width"
            android:maxWidth="@dimen/qs_light_width"
            android:minHeight="@dimen/qs_volume_dialog_height"
            android:maxHeight="@dimen/qs_volume_dialog_height"
            android:progressDrawable="@drawable/ic_seek_bar_progress_background"
            android:thumb="@null"
            android:focusable="false" />

        <ImageView
            android:id="@+id/clock_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/menu_clock_small"
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="17dp" />

    </RelativeLayout>

</RelativeLayout>
