<?xml version="1.0" encoding="utf-8"?>
<!--
**
** Copyright 2006, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<com.android.systemui.statusbar.phone.NotificationPanelView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res-auto"
    android:id="@+id/notification_panel"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        layout="@layout/keyguard_status_view"
        android:visibility="gone" />

    <com.android.systemui.statusbar.phone.NotificationsQuickSettingsContainer
        android:id="@+id/notification_container_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="@integer/notification_panel_layout_gravity"
        android:visibility="visible">

        <FrameLayout
            android:id="@+id/qs_frame"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout="@layout/qs_panel"
            android:layout_marginLeft="640dp"
            systemui:viewType="com.android.systemui.plugins.qs.QS" />

        <com.android.systemui.statusbar.stack.NotificationStackScrollLayout
            android:id="@+id/notification_stack_scroller"
            android:layout_marginTop="@dimen/notification_panel_margin_top"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="@integer/notification_panel_layout_gravity"
            android:layout_marginBottom="@dimen/close_handle_underlap" />

        <include layout="@layout/ambient_indication"
            android:id="@+id/ambient_indication_container"
            android:visibility="gone"/>

        <ViewStub
            android:id="@+id/keyguard_user_switcher"
            android:layout="@layout/keyguard_user_switcher"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:visibility="gone"/>

        <include
            layout="@layout/keyguard_status_bar"
            android:visibility="gone" />

        <Button
            android:id="@+id/report_rejected_touch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/status_bar_header_height_keyguard"
            android:text="@string/report_rejected_touch"
            android:visibility="gone" />

    </com.android.systemui.statusbar.phone.NotificationsQuickSettingsContainer>

    <include
        layout="@layout/keyguard_bottom_area"
        android:visibility="gone" />

    <com.android.systemui.statusbar.AlphaOptimizedView
        android:id="@+id/qs_navbar_scrim"
        android:layout_height="96dp"
        android:layout_width="match_parent"
        android:layout_gravity="bottom"
        android:visibility="invisible"
        android:background="@drawable/qs_navbar_scrim" />

</com.android.systemui.statusbar.phone.NotificationPanelView>
