<?xml version="1.0" encoding="utf-8"?><!--
** Copyright 2017, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:systemui="http://schemas.android.com/apk/res-auto"
    android:id="@+id/quick_status_bar_system_icons"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_marginLeft="@dimen/quick_status_bar_system_icons_margin_left"
    android:layout_marginTop="@dimen/qs_panel_time_top_margin"
    android:paddingStart="@dimen/status_bar_padding_start">

    <com.android.systemui.statusbar.policy.DateView
        android:id="@+id/date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/quick_status_bar_system_icons_date_margin_left"
        android:singleLine="true"
        android:textColor="@color/notification_panel_head_date_color"
        android:textSize="20sp"
        systemui:datePattern="@string/abbrev_wday_month_day_no_year_alarm" />
</LinearLayout>
