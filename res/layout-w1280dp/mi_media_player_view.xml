<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/media_relative_layout"
    android:layout_width="@dimen/qs_media_player_view_width"
    android:layout_height="@dimen/qs_media_player_view_height"
    android:background="@drawable/ic_qs_rectangle">

    <!-- 音乐封面卡片 -->
    <android.support.v7.widget.CardView
        android:id="@+id/music_guide_card"
        android:layout_width="@dimen/mi_media_cover_size"
        android:layout_height="@dimen/mi_media_cover_size"
        android:layout_marginStart="@dimen/mi_media_cover_left_margin"
        android:visibility="visible"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="@dimen/mi_media_cover_radius"
        app:cardElevation="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/title"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintVertical_bias="0.5">

        <ImageView
            android:id="@+id/music_guide"
            android:layout_width="@dimen/mi_media_cover_size"
            android:layout_height="@dimen/mi_media_cover_size"
            android:layout_gravity="center_vertical"
            android:src="@drawable/media_default_icon" />
    </android.support.v7.widget.CardView>

    <!-- 标题和艺术家文本 -->
    <com.android.systemui.qs.mi.MarqueeTextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:maxWidth="@dimen/mi_media_tile_width"
        android:layout_height="wrap_content"
        android:ellipsize="marquee"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:text="@string/qs_player_title_default"
        android:textColor="@color/color_white"
        android:textSize="@dimen/text_size_21"
        android:layout_marginTop="@dimen/qs_media_title_text_margin_top"
        app:layout_constraintTop_toBottomOf="@id/music_guide_card"
        app:layout_constraintBottom_toTopOf="@id/artist"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
     

    <com.android.systemui.qs.mi.MarqueeTextView
        android:id="@+id/artist"
        android:layout_width="wrap_content"
        android:maxWidth="@dimen/mi_media_tile_width"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/qs_media_desc_padding_top"
        android:ellipsize="marquee"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:textColor="@color/color_white_40"
        android:textSize="@dimen/text_size_18"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:layout_constraintBottom_toTopOf="@id/prev_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


    <!-- 播放控制按钮 -->
    <ImageView
        android:id="@+id/prev_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/mi_play_button_margin_linear_top"
        android:src="@drawable/menu_prev_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/play_btn"
        app:layout_constraintTop_toBottomOf="@id/artist"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_bias="0.5" />  <!-- 关键：水平居中 -->

    <ImageView
        android:id="@+id/play_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/menu_pause_btn"
        app:layout_constraintStart_toEndOf="@id/prev_btn"
        app:layout_constraintEnd_toStartOf="@id/next_btn"
        app:layout_constraintTop_toTopOf="@id/prev_btn"
        app:layout_constraintBottom_toBottomOf="@id/prev_btn" />

    <ImageView
        android:id="@+id/next_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/menu_next_btn"
        app:layout_constraintStart_toEndOf="@id/play_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/play_btn"
        app:layout_constraintBottom_toBottomOf="@id/play_btn" />

</android.support.constraint.ConstraintLayout>