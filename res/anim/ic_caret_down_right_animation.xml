<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<set
    xmlns:android="http://schemas.android.com/apk/res/android" >
    <objectAnimator
        android:duration="250"
        android:propertyXName="translateX"
        android:propertyYName="translateY"
        android:pathData="M 11.287,8.701 c 0.0,1.09767 0.0,5.48833 0.0,6.586"
        android:interpolator="@android:interpolator/fast_out_slow_in" />
    <objectAnimator
        android:duration="200"
        android:propertyName="rotation"
        android:valueFrom="45.0"
        android:valueTo="-45.0"
        android:valueType="floatType"
        android:interpolator="@interpolator/ic_caret_down_animation_interpolator_0" />
</set>
