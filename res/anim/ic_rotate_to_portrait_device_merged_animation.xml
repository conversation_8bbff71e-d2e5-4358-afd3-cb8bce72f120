<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<set
    xmlns:android="http://schemas.android.com/apk/res/android" >
    <set
        android:ordering="sequentially" >
        <objectAnimator
            android:duration="50"
            android:propertyName="pathData"
            android:valueFrom="M -3.34053039551,-22.********** c -1.**********,-1.********** -3.46876525879,-1.26383972168 -4.74829101563,0.125762939453 c 0.0,0.0 -14.**********,14.********** -14.**********,14.********** c -1.39259338379,1.392578125 -1.44947814941,3.54061889648 -0.125762939453,4.74827575684 c 0.0,0.0 26.**********,26.********** 26.**********,26.********** c 1.**********,1.********** 3.46876525879,1.26382446289 4.74829101562,-0.125762939453 c 0.0,0.0 14.**********,-14.********** 14.**********,-14.********** c 1.392578125,-1.39259338379 1.44947814941,-3.54061889648 0.125762939453,-4.74829101562 c 0.0,0.0 -26.**********,-26.417388916 -26.**********,-26.417388916 Z M 2.87156677246,16.********** c 0.0,0.0 -19.**********,-19.********** -19.**********,-19.********** c 0.0,0.0 14.0142059326,-14.2142181396 14.0142059326,-14.2142181396 c 0.0,0.0 19.**********,19.********** 19.**********,19.********** c 0.0,0.0 -14.0142211914,14.2142181396 -14.0142211914,14.2142181396 Z"
            android:valueTo="M -3.34053039551,-22.********** c -1.**********,-1.********** -3.46876525879,-1.26383972168 -4.74829101563,0.125762939453 c 0.0,0.0 -14.**********,14.********** -14.**********,14.********** c -1.39259338379,1.392578125 -1.44947814941,3.54061889648 -0.125762939453,4.74827575684 c 0.0,0.0 26.**********,26.********** 26.**********,26.********** c 1.**********,1.********** 3.46876525879,1.26382446289 4.74829101562,-0.125762939453 c 0.0,0.0 14.**********,-14.********** 14.**********,-14.********** c 1.392578125,-1.39259338379 1.44947814941,-3.54061889648 0.125762939453,-4.74829101562 c 0.0,0.0 -26.**********,-26.417388916 -26.**********,-26.417388916 Z M 2.87156677246,16.********** c 0.0,0.0 -19.**********,-19.********** -19.**********,-19.********** c 0.0,0.0 14.0142059326,-14.2142181396 14.0142059326,-14.2142181396 c 0.0,0.0 19.**********,19.********** 19.**********,19.********** c 0.0,0.0 -14.0142211914,14.2142181396 -14.0142211914,14.2142181396 Z"
            android:valueType="pathType"
            android:interpolator="@android:interpolator/linear" />
        <objectAnimator
            android:duration="500"
            android:propertyName="pathData"
            android:valueFrom="M -3.34053039551,-22.********** c -1.**********,-1.********** -3.46876525879,-1.26383972168 -4.74829101563,0.125762939453 c 0.0,0.0 -14.**********,14.********** -14.**********,14.********** c -1.39259338379,1.392578125 -1.44947814941,3.54061889648 -0.125762939453,4.74827575684 c 0.0,0.0 26.**********,26.********** 26.**********,26.********** c 1.**********,1.********** 3.46876525879,1.26382446289 4.74829101562,-0.125762939453 c 0.0,0.0 14.**********,-14.********** 14.**********,-14.********** c 1.392578125,-1.39259338379 1.44947814941,-3.54061889648 0.125762939453,-4.74829101562 c 0.0,0.0 -26.**********,-26.417388916 -26.**********,-26.417388916 Z M 2.87156677246,16.********** c 0.0,0.0 -19.**********,-19.********** -19.**********,-19.********** c 0.0,0.0 14.0142059326,-14.2142181396 14.0142059326,-14.2142181396 c 0.0,0.0 19.**********,19.********** 19.**********,19.********** c 0.0,0.0 -14.0142211914,14.2142181396 -14.0142211914,14.2142181396 Z"
            android:valueTo="M -3.5,-20.5 c -1.19999694824,-1.19999694824 -3.10000610352,-1.19999694824 -4.19999694824,0.0 c 0.0,0.0 -12.8000030518,12.6999969482 -12.8000030518,12.6999969482 c -1.19999694824,1.19999694824 -1.19999694824,3.10000610352 0.0,4.19999694824 c 0.0,0.0 24.0,24.0000152588 24.0,24.0000152588 c 1.19999694824,1.19999694824 3.10000610352,1.19999694824 4.19999694824,0.0 c 0.0,0.0 12.6999969482,-12.700012207 12.6999969482,-12.700012207 c 1.20001220703,-1.19999694824 1.20001220703,-3.09999084473 0.0,-4.19999694824 c 0.0,0.0 -23.8999938965,-24.0 -23.8999938965,-24.0 Z M 2.84999084473,15.5500183105 c 0.0,0.0 -18.6000061035,-18.5000457764 -18.6000061035,-18.5000457764 c 0.0,0.0 12.5999908447,-12.8000030518 12.5999908447,-12.8000030518 c 0.0,0.0 18.6000213623,18.5000457764 18.6000213623,18.5000457764 c 0.0,0.0 -12.6000061035,12.8000030518 -12.6000061035,12.8000030518 Z"
            android:valueType="pathType"
            android:interpolator="@android:interpolator/fast_out_slow_in" />
    </set>
</set>
