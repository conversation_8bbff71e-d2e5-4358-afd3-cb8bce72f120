<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<set xmlns:android="http://schemas.android.com/apk/res/android" >
    <objectAnimator
        android:duration="350"
        android:propertyName="pathData"
        android:valueFrom="M 37.**********,-40.********** c 0.0,0.0 -35.**********,31.********** -35.**********,31.********** c 0.0,0.0 40.**********,40.********** 40.**********,40.********** c 0.0,0.0 -2.61700439453,2.********** -2.61700439453,2.********** c 0.0,0.0 -41.**********,-40.********** -41.**********,-40.********** c 0.0,0.0 -34.**********,25.********** -34.**********,25.********** c 0.0,0.0 55.**********,69.742401123 55.**********,69.742401123 c 0.0,0.0 73.**********,-59.********** 73.**********,-59.********** c 0.0,0.0 -55.**********,-69.********** -55.**********,-69.********** Z"
        android:valueTo="M 37.**********,-40.********** c 0.0,0.0 -35.**********,31.********** -35.**********,31.********** c 0.0,0.0 9.55097961426,9.55285644531 9.55097961426,9.55285644531 c 0.0,0.0 -2.61698913574,2.09387207031 -2.61698913574,2.09387207031 c 0.0,0.0 -9.75096130371,-9.56428527832 -9.75096130371,-9.56428527832 c 0.0,0.0 -34.**********,25.********** -34.**********,25.********** c 0.0,0.0 55.**********,69.742401123 55.**********,69.742401123 c 0.0,0.0 73.**********,-59.********** 73.**********,-59.********** c 0.0,0.0 -55.**********,-69.********** -55.**********,-69.********** Z"
        android:valueType="pathType"
        android:interpolator="@interpolator/ic_signal_workmode_enable_mask_pathdata_interpolator" />
</set>
