<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2017 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<set
    xmlns:android="http://schemas.android.com/apk/res/android" >
    <set
        android:ordering="sequentially" >
        <objectAnimator
            android:duration="200"
            android:propertyName="pathData"
            android:valueFrom="M 0.0,-7.0 l 0.0,0.0 c 1.**********,0.0 3.5,1.********** 3.5,3.5 l 0.0,7.0 c 0.0,1.********** -1.**********,3.5 -3.5,3.5 l 0.0,0.0 c -1.**********,0.0 -3.5,-1.********** -3.5,-3.5 l 0.0,-7.0 c 0.0,-1.********** 1.**********,-3.5 3.5,-3.5 Z"
            android:valueTo="M 0.0,-7.0 l 0.0,0.0 c 1.**********,0.0 3.5,1.********** 3.5,3.5 l 0.0,7.0 c 0.0,1.********** -1.**********,3.5 -3.5,3.5 l 0.0,0.0 c -1.**********,0.0 -3.5,-1.********** -3.5,-3.5 l 0.0,-7.0 c 0.0,-1.********** 1.**********,-3.5 3.5,-3.5 Z"
            android:valueType="pathType"
            android:interpolator="@android:interpolator/linear" />
        <objectAnimator
            android:duration="300"
            android:propertyName="pathData"
            android:valueFrom="M 0.0,-7.0 l 0.0,0.0 c 1.**********,0.0 3.5,1.********** 3.5,3.5 l 0.0,7.0 c 0.0,1.********** -1.**********,3.5 -3.5,3.5 l 0.0,0.0 c -1.**********,0.0 -3.5,-1.********** -3.5,-3.5 l 0.0,-7.0 c 0.0,-1.********** 1.**********,-3.5 3.5,-3.5 Z"
            android:valueTo="M 0.0,-3.0 l 0.0,0.0 c 0.5522847498,0.0 1.0,0.4477152502 1.0,1.0 l 0.0,4.0 c 0.0,0.5522847498 -0.4477152502,1.0 -1.0,1.0 l 0.0,0.0 c -0.5522847498,0.0 -1.0,-0.4477152502 -1.0,-1.0 l 0.0,-4.0 c 0.0,-0.5522847498 0.4477152502,-1.0 1.0,-1.0 Z"
            android:valueType="pathType"
            android:interpolator="@interpolator/trusted_state_to_error_animation_interpolator_2" />
    </set>
    <set
        android:ordering="sequentially" >
        <objectAnimator
            android:duration="183"
            android:propertyName="fillAlpha"
            android:valueFrom="0.0"
            android:valueTo="0.0"
            android:valueType="floatType"
            android:interpolator="@android:interpolator/linear" />
        <objectAnimator
            android:duration="16"
            android:propertyName="fillAlpha"
            android:valueFrom="0.0"
            android:valueTo="1.0"
            android:valueType="floatType"
            android:interpolator="@android:interpolator/linear" />
    </set>
</set>
