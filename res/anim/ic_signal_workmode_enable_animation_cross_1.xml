<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<set xmlns:android="http://schemas.android.com/apk/res/android" >
    <objectAnimator
        android:duration="350"
        android:propertyName="pathData"
        android:valueFrom="M 7.54049682617,3.********** c 0.0,0.0 31.**********,31.********** 31.**********,31.********** "
        android:valueTo="M 7.54049682617,3.********** c 0.0,0.0 0.324981689453,0.399978637695 0.324981689453,0.399978637695 "
        android:valueType="pathType"
        android:interpolator="@interpolator/ic_signal_workmode_enable_cross_1_pathdata_interpolator" />
    <set
        android:ordering="sequentially" >
        <objectAnimator
            android:duration="333"
            android:propertyName="strokeAlpha"
            android:valueFrom="1"
            android:valueTo="1"
            android:interpolator="@android:interpolator/linear" />
        <objectAnimator
            android:duration="17"
            android:propertyName="strokeAlpha"
            android:valueFrom="1"
            android:valueTo="0"
            android:interpolator="@android:interpolator/linear" />
    </set>
</set>
