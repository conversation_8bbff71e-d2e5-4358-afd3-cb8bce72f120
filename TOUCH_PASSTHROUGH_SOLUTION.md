# MiRightNotificationContainer 触摸穿透解决方案

## 问题描述
MiRightNotificationContainer 是一个常驻通知的父容器，设置了全屏宽度 (`MATCH_PARENT`)，但实际的通知内容（RemoteView）可能只占用部分宽度（如500px）。这导致左右两侧的空白区域（各250px）虽然能看到下面的组件，但点击没有响应。

## 解决方案

### 1. 修改 WindowManager.LayoutParams 标志
在 `init()` 方法中，为 WindowManager.LayoutParams 添加了以下标志：

```java
layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
        | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
        | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;
```

- `FLAG_NOT_TOUCH_MODAL`: 允许触摸事件穿透到下层窗口
- `FLAG_NOT_FOCUSABLE`: 窗口不获取焦点
- `FLAG_WATCH_OUTSIDE_TOUCH`: 监听外部触摸事件

### 2. 重写触摸事件处理方法
添加了两个关键的触摸事件处理方法：

#### `onTouchEvent(MotionEvent event)`
- 检查触摸点是否在通知内容区域内
- 如果在内容区域内，正常处理触摸事件
- 如果在空白区域，返回 `false` 让事件穿透到下层

#### `onInterceptTouchEvent(MotionEvent event)`
- 检查触摸点是否在通知内容区域内
- 如果在内容区域内，拦截事件进行处理
- 如果在空白区域，返回 `false` 不拦截事件，让事件穿透到下层

### 3. 添加辅助方法
#### `isTouchInNotificationArea(MotionEvent event)`
- 检查当前通知项是否存在且可见
- 获取通知项的边界矩形
- 判断触摸点是否在边界矩形内

## 技术细节

### 导入的新包
```java
import android.graphics.Rect;
import android.view.MotionEvent;
```

### 核心逻辑
1. 容器保持全屏宽度以确保通知能够正确显示
2. 通过重写触摸事件方法，只在通知内容区域响应触摸
3. 空白区域的触摸事件会穿透到下层组件，实现点击穿透效果

## 优势
1. **保持原有布局**: 容器仍然是全屏宽度，不影响通知的显示效果
2. **精确的触摸控制**: 只在通知内容区域响应触摸，空白区域完全穿透
3. **性能优化**: 使用 `getHitRect()` 方法获取精确的触摸边界，避免不必要的计算
4. **兼容性好**: 不影响现有的通知显示逻辑

## 使用场景
这个解决方案特别适用于：
- 常驻通知容器
- 需要部分区域触摸穿透的悬浮窗
- 自定义 RemoteView 通知显示

## 注意事项
1. 确保 `mCurrentItem` 在通知显示时正确设置
2. 触摸事件的坐标是相对于容器的本地坐标
3. 需要在通知隐藏时正确清理 `mCurrentItem` 引用
