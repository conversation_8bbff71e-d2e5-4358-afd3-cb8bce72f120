# MiRightNotificationContainer 触摸穿透解决方案

## 问题描述
MiRightNotificationContainer 是一个常驻通知的父容器，设置了全屏宽度 (`MATCH_PARENT`)，但实际的通知内容（RemoteView）可能只占用部分宽度（如500px）。这导致左右两侧的空白区域（各250px）虽然能看到下面的组件，但点击没有响应。

## 最终解决方案：动态窗口大小调整

经过分析和测试，我们采用了**动态调整窗口大小**的方案，这是最可靠和有效的解决方法。

### 核心思路
不再使用全屏宽度的窗口，而是根据通知内容的实际大小动态调整窗口大小，这样就不会有空白区域拦截触摸事件。

### 主要修改

#### 1. 添加动态窗口大小调整方法
```java
private void updateWindowSizeToContent() {
    if (mWindowManager == null || mCurrentItem == null) return;

    // 强制测量通知项的大小
    mCurrentItem.measure(
        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
    );

    int itemWidth = mCurrentItem.getMeasuredWidth();
    int itemHeight = mCurrentItem.getMeasuredHeight();

    // 调整窗口大小为通知内容的实际大小
    WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
    layoutParams.width = Math.max(itemWidth, 200); // 最小宽度200px
    layoutParams.height = itemHeight + mTopMargin;
    layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;

    mWindowManager.updateViewLayout(this, layoutParams);
}
```

#### 2. 在通知显示时调用窗口大小调整
在 `addStandardNotificationItem()` 和 `addCustomNotificationItem()` 方法中，添加延迟调用：

```java
// 在下一个UI循环中更新窗口大小，确保view已经完成布局
postDelayed(new Runnable() {
    @Override
    public void run() {
        updateWindowSizeToContent();
    }
}, 50); // 延迟50ms确保布局完成
```

#### 3. 在通知隐藏时重置窗口
```java
private void resetToPassthroughMode() {
    WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
    if (layoutParams != null) {
        // 恢复全屏宽度和穿透模式
        layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        mWindowManager.updateViewLayout(this, layoutParams);
    }
}
```

#### 4. 添加详细的调试日志
使用 tag "yyh123" 添加了详细的日志，帮助调试：
- 窗口大小调整过程
- 触摸事件处理过程
- 通知项边界检测

### 工作流程
1. **通知显示时**：
   - 添加通知项到容器
   - 延迟50ms后测量通知项实际大小
   - 动态调整窗口大小匹配通知内容
   - 移除FLAG_NOT_TOUCH_MODAL，让窗口正常处理触摸

2. **通知隐藏时**：
   - 重置窗口为全屏宽度
   - 添加FLAG_NOT_TOUCH_MODAL，让窗口完全穿透

### 优势
1. **彻底解决问题**: 没有空白区域，自然不会拦截触摸事件
2. **性能最优**: 不需要复杂的触摸事件判断和穿透逻辑
3. **兼容性好**: 不影响现有的通知显示和交互逻辑
4. **调试友好**: 详细的日志帮助排查问题

### 调试方法
运行修改后的代码，查看logcat中tag为"yyh123"的日志：
```bash
adb logcat | grep yyh123
```

关键日志包括：
- 窗口大小调整：`updateWindowSizeToContent: updated window size to`
- 通知项设置：`addStandardNotificationItem: mCurrentItem set to`
- 触摸事件处理：`dispatchTouchEvent: touch in blank area`

这个解决方案应该能够完全解决触摸穿透问题。
