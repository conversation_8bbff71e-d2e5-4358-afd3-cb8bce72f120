package com.android.systemui.qs.tiles;

import android.annotation.TargetApi;
import android.app.AppComponentFactory;
import android.app.Application;
import android.app.UiModeManager;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.database.ContentObserver;
import android.graphics.Rect;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.provider.Settings;
import android.service.quicksettings.Tile;
import android.util.Log;

import com.android.systemui.Dependency;
import com.android.systemui.R;
import com.android.systemui.plugins.qs.QSTile;
import com.android.systemui.qs.QSHost;
import com.android.systemui.qs.tileimpl.QSTileImpl;
import com.android.systemui.qs.tileimpl.QSTitleExtraInterface;
import com.android.internal.logging.nano.MetricsProto;
import com.android.systemui.statusbar.policy.DarkIconDispatcher;
import com.android.systemui.toast.ToastWithPicture;


public class UiThemeTile extends QSTileImpl<QSTile.BooleanState> implements QSTitleExtraInterface, DarkIconDispatcher.DarkReceiver {

    private static final String TAG = "UiModeTile";
    private boolean mColorModeActive;
    UiModeManager uiModeManager;

    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
    public UiThemeTile(QSHost host) {
        super(host);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.FROYO) {
            uiModeManager = (UiModeManager) mContext.getSystemService(Context.UI_MODE_SERVICE);
        }
        boolean isDarkMode = checkUiThemeMode();
        if (isDarkMode) {
            mColorModeActive = true;
        } else {
            mColorModeActive = false;
        }
        Log.d(TAG, "UiThemeTile: register observer");
        Dependency.get(DarkIconDispatcher.class).addDarkReceiver(this);
    }

    @Override
    public BooleanState newTileState() {
        return new BooleanState();
    }

    @Override
    protected void handleClick() {
        handleLongClick();
    }

    public void handleLongClick() {
        switchColorMode();
    }

    @TargetApi(Build.VERSION_CODES.FROYO)
    public void switchColorMode() {
        boolean isDarkMode = checkUiThemeMode();
        if (!isDarkMode) {
            uiModeManager.setNightMode(UiModeManager.MODE_NIGHT_YES);
            mColorModeActive = true;
        } else {
            uiModeManager.setNightMode(UiModeManager.MODE_NIGHT_NO);
            mColorModeActive = false;
        }
        showPicToast(mColorModeActive);
        refreshState();
    }

    private boolean checkUiThemeMode() {
        int currentMode = getUiMode();
        Log.d(TAG, "checkUiThemeMode: current mode is:" + currentMode);
        boolean isDarkMode;
        if (currentMode == UiModeManager.MODE_NIGHT_YES) {
            isDarkMode = true;
        } else {
            isDarkMode = false;
        }
        return isDarkMode;
    }

    private void showPicToast(boolean on) {
        if (on) {
            ToastWithPicture.getInstance().showToastDelayed(mContext, R.string.theme_mode_dark_on);
        } else {
            ToastWithPicture.getInstance().showToastDelayed(mContext, R.string.theme_mode_dark_off);
        }
    }

    public int getUiMode() {
        int currentMode = 0;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.FROYO) {
            currentMode = mContext.getResources().getConfiguration().uiMode & Configuration.UI_MODE_NIGHT_MASK;
        }
        Log.d(TAG, "switchColorMode:current mode:" + currentMode);
        return currentMode >> 4;
    }

    @Override
    protected void handleUpdateState(BooleanState state, Object arg) {
        if (mColorModeActive) {
            state.icon = ResourceIcon.get(R.drawable.ic_qs_color_mode_off);
            state.state = Tile.STATE_ACTIVE;
        } else {
            state.icon = ResourceIcon.get(R.drawable.ic_qs_color_mode_off);
            state.state = Tile.STATE_INACTIVE;
        }
        state.activateColor = mContext.getColor(R.color.system_ui_activate_color_blue);
    }

    @Override
    public int getMetricsCategory() {
        return MetricsProto.MetricsEvent.QS_INTENT;
    }

    @Override
    public Intent getLongClickIntent() {
        return null;
    }

    @Override
    protected void handleSetListening(boolean listening) {
        // do nothing
    }

    @Override
    public CharSequence getTileLabel() {
        return mContext.getString(R.string.qs_ui_mode_tile);
    }

    @Override
    public int getViewId() {
        return R.id.mico_qs_panel_title_ui_mode;
    }

    @Override
    public void onDarkChanged(Rect area, float darkIntensity, int tint) {
        Log.d(TAG, "onDarkChanged: dark changed:");
        mColorModeActive = checkUiThemeMode();
        refreshState();
    }
}
