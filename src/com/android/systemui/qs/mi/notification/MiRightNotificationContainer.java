package com.android.systemui.qs.mi.notification;

import android.content.Context;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import com.android.systemui.R;
import com.android.systemui.statusbar.phone.StatusBar;

// 每次只有一个View展示
// Top 消息通知
public class MiRightNotificationContainer extends RelativeLayout implements MiNotificationInternalListener {

    private static final String TAG = "MiRightNotificationContainer";
    private static final int HIDE_NOTIFICATION_DELAY = 3500;

    protected final Context mContext;
    protected MiStatusBarNotification mCurrentSbn;
    protected View mCurrentItem;

    protected WindowManager mWindowManager;
    private StatusBar mStatusBar;
    protected int mTopMargin;

    public MiRightNotificationContainer(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public void setStatusBar(StatusBar statusBar) {
        mStatusBar = statusBar;
    }

    // 如果是正在显示横幅消息，就不显示状态栏上的图标
    public boolean isShowing() {
        return getVisibility() == View.VISIBLE;
    }

    public void hide() {
        setVisibility(View.GONE);
    }

    public void dismiss() {
        hide();
    }

    public void show() {
        setVisibility(View.VISIBLE);
    }

    @Override
    public void onNotificationPosted(MiStatusBarNotification miSbn) {
        if (miSbn == null) return;
        if (miSbn.isPersistentNotification()) {
            return;
        }
        Log.d(TAG, "onNotificationPosted, " + miSbn.getUniqueId());
        if (miSbn.isForbidPopupNotification()) {
            Log.d(TAG, "forbid popup notification.");
            return;
        }
        if (MiNotificationCenterData.getInstance().isCenterActivityResumed()) {
            return;
        }

        addNotificationItem(miSbn);
        show();
    }

    @Override
    public void onNotificationRemoved(MiStatusBarNotification miSbn) {
        if (miSbn == null || mCurrentSbn == null) return;
        if (miSbn.isPersistentNotification()) {
            return;
        }
        Log.d(TAG, "onNotificationRemoved, " + miSbn.getUniqueId());
        if (mCurrentSbn.getUniqueId().equals(miSbn.getUniqueId()) && mCurrentItem.isAttachedToWindow()) {
            this.removeView(mCurrentItem);
        }
        removeCallbacks(mHideRunnable);

        dismiss();
    }

    private void init() {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        // 添加FLAG_NOT_TOUCH_MODAL让空白区域的触摸事件能够穿透到下层
        // 添加FLAG_WATCH_OUTSIDE_TOUCH来监听外部触摸事件
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.TOP;
        layoutParams.format = PixelFormat.TRANSLUCENT;
        layoutParams.windowAnimations = R.style.Animation_Notification;
        mWindowManager = mContext.getSystemService(WindowManager.class);
        mWindowManager.addView(this, layoutParams);
        hide();

        mTopMargin = getResources().getDimensionPixelSize(R.dimen.mi_notification_view_top_margin);
    }

    public void deInit() {
        mStatusBar = null;
        if (mWindowManager != null) {
            mWindowManager.removeView(this);
            mWindowManager = null;
        }
        MiNotificationManager.getManager(mContext).unregisterInternalMiListener(this);
    }

    public void addNotificationItem(MiStatusBarNotification miSbn) {
        Log.d(TAG, "show notification view");
        
        // 检查是否有自定义RemoteView
        if (miSbn.hasCustomContentView()) {
            // 使用自定义RemoteView
            addCustomNotificationItem(miSbn);
        } else {
            // 使用标准通知视图
            addStandardNotificationItem(miSbn);
        }
    }
    
    /**
     * 添加标准通知项
     */
    private void addStandardNotificationItem(MiStatusBarNotification miSbn) {
        MiRightNotificationItem view = new MiRightNotificationItem(mContext);
        view.setUp(miSbn, this);
        view.setYThreshold(mTopMargin);
        this.removeAllViews();

        RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(
                LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        rlp.topMargin = mTopMargin;
        rlp.addRule(RelativeLayout.CENTER_HORIZONTAL);
        this.addView(view, rlp);
        mCurrentItem = view;
        mCurrentSbn = miSbn;

        view.setOnClickListener(v -> view.executePendingIntent());

        removeCallbacks(mHideRunnable);
        // 停留几秒后隐藏
        postDelayed(mHideRunnable, HIDE_NOTIFICATION_DELAY);
        
        Log.d(TAG, "Added standard notification item");
    }
    
    /**
     * 添加自定义通知项
     */
    private void addCustomNotificationItem(MiStatusBarNotification miSbn) {
        try {
            // 创建自定义通知视图项
            MiRightCustomNotificationItem customView = new MiRightCustomNotificationItem(mContext);
            customView.setUp(miSbn, this);
            customView.setYThreshold(mTopMargin);
            
            // 添加到容器中
            this.removeAllViews();
            
            RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(
                    LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT); 
            
            rlp.topMargin = mTopMargin;
            rlp.addRule(RelativeLayout.CENTER_HORIZONTAL);
            this.addView(customView, rlp);
            
            mCurrentItem = customView;
            mCurrentSbn = miSbn;
            
            customView.setOnClickListener(v -> customView.executePendingIntent());
            
            removeCallbacks(mHideRunnable);
            // 停留几秒后隐藏
            postDelayed(mHideRunnable, HIDE_NOTIFICATION_DELAY);
            
            Log.d(TAG, "Added custom notification item using MiRightCustomNotificationItem11");
        } catch (Exception e) {
            Log.e(TAG, "Error adding custom notification item", e);
            // 如果自定义视图添加失败，回退到标准视图
            addStandardNotificationItem(miSbn);
        }
    }

    private final Runnable mHideRunnable = () -> {
        Log.d(TAG, "hide top notification");
        dismiss();
    };

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // 检查触摸点是否在通知内容区域内
        if (isTouchInNotificationArea(event)) {
            // 触摸在通知内容区域内，正常处理
            return super.onTouchEvent(event);
        } else {
            // 触摸在空白区域，不处理触摸事件，让事件穿透到下层
            return false;
        }
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        // 检查触摸点是否在通知内容区域内
        if (isTouchInNotificationArea(event)) {
            // 触摸在通知内容区域内，拦截事件进行处理
            return super.onInterceptTouchEvent(event);
        } else {
            // 触摸在空白区域，不拦截事件，让事件穿透到下层
            return false;
        }
    }

    /**
     * 检查触摸点是否在通知内容区域内
     */
    private boolean isTouchInNotificationArea(MotionEvent event) {
        if (mCurrentItem == null || mCurrentItem.getVisibility() != View.VISIBLE) {
            return false;
        }

        // 获取当前通知项的边界
        Rect itemBounds = new Rect();
        mCurrentItem.getHitRect(itemBounds);

        // 检查触摸点是否在通知项的范围内
        int x = (int) event.getX();
        int y = (int) event.getY();

        return itemBounds.contains(x, y);
    }
}
