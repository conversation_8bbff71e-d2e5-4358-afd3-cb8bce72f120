package com.android.systemui.qs.mi.notification;

import android.content.Context;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import com.android.systemui.R;
import com.android.systemui.statusbar.phone.StatusBar;

// 每次只有一个View展示
// Top 消息通知
public class MiRightNotificationContainer extends RelativeLayout implements MiNotificationInternalListener {

    private static final String TAG = "MiRightNotificationContainer";
    private static final int HIDE_NOTIFICATION_DELAY = 3500;

    protected final Context mContext;
    protected MiStatusBarNotification mCurrentSbn;
    protected View mCurrentItem;

    protected WindowManager mWindowManager;
    private StatusBar mStatusBar;
    protected int mTopMargin;

    public MiRightNotificationContainer(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public void setStatusBar(StatusBar statusBar) {
        mStatusBar = statusBar;
    }

    // 如果是正在显示横幅消息，就不显示状态栏上的图标
    public boolean isShowing() {
        return getVisibility() == View.VISIBLE;
    }

    public void hide() {
        setVisibility(View.GONE);
        Log.d("yyh123", "hide: notification hidden, resetting to passthrough mode");
        resetToPassthroughMode();
    }

    public void dismiss() {
        hide();
        Log.d("yyh123", "dismiss: notification dismissed");
    }

    public void show() {
        setVisibility(View.VISIBLE);
    }

    @Override
    public void onNotificationPosted(MiStatusBarNotification miSbn) {
        if (miSbn == null) return;
        if (miSbn.isPersistentNotification()) {
            return;
        }
        Log.d(TAG, "onNotificationPosted, " + miSbn.getUniqueId());
        if (miSbn.isForbidPopupNotification()) {
            Log.d(TAG, "forbid popup notification.");
            return;
        }
        if (MiNotificationCenterData.getInstance().isCenterActivityResumed()) {
            return;
        }

        addNotificationItem(miSbn);
        show();
    }

    @Override
    public void onNotificationRemoved(MiStatusBarNotification miSbn) {
        if (miSbn == null || mCurrentSbn == null) return;
        if (miSbn.isPersistentNotification()) {
            return;
        }
        Log.d(TAG, "onNotificationRemoved, " + miSbn.getUniqueId());
        if (mCurrentSbn.getUniqueId().equals(miSbn.getUniqueId()) && mCurrentItem.isAttachedToWindow()) {
            this.removeView(mCurrentItem);
        }
        removeCallbacks(mHideRunnable);

        dismiss();
    }

    private void init() {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        // 关键修改：添加FLAG_NOT_TOUCH_MODAL让空白区域的触摸事件能够穿透到下层
        // 移除FLAG_WATCH_OUTSIDE_TOUCH，因为它可能会干扰事件穿透
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.TOP;
        layoutParams.format = PixelFormat.TRANSLUCENT;
        layoutParams.windowAnimations = R.style.Animation_Notification;
        mWindowManager = mContext.getSystemService(WindowManager.class);
        mWindowManager.addView(this, layoutParams);
        hide();

        mTopMargin = getResources().getDimensionPixelSize(R.dimen.mi_notification_view_top_margin);

        Log.d("yyh123", "MiRightNotificationContainer init completed with FLAG_NOT_TOUCH_MODAL");
    }

    public void deInit() {
        mStatusBar = null;
        if (mWindowManager != null) {
            mWindowManager.removeView(this);
            mWindowManager = null;
        }
        MiNotificationManager.getManager(mContext).unregisterInternalMiListener(this);
    }

    public void addNotificationItem(MiStatusBarNotification miSbn) {
        Log.d(TAG, "show notification view");
        
        // 检查是否有自定义RemoteView
        if (miSbn.hasCustomContentView()) {
            // 使用自定义RemoteView
            addCustomNotificationItem(miSbn);
        } else {
            // 使用标准通知视图
            addStandardNotificationItem(miSbn);
        }
    }
    
    /**
     * 添加标准通知项
     */
    private void addStandardNotificationItem(MiStatusBarNotification miSbn) {
        MiRightNotificationItem view = new MiRightNotificationItem(mContext);
        view.setUp(miSbn, this);
        view.setYThreshold(mTopMargin);
        this.removeAllViews();

        RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(
                LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        rlp.topMargin = mTopMargin;
        rlp.addRule(RelativeLayout.CENTER_HORIZONTAL);
        this.addView(view, rlp);
        mCurrentItem = view;
        mCurrentSbn = miSbn;

        view.setOnClickListener(v -> view.executePendingIntent());

        removeCallbacks(mHideRunnable);
        // 停留几秒后隐藏
        postDelayed(mHideRunnable, HIDE_NOTIFICATION_DELAY);

        Log.d(TAG, "Added standard notification item");
        Log.d("yyh123", "addStandardNotificationItem: mCurrentItem set to " + mCurrentItem);

        // 在下一个UI循环中更新窗口大小，确保view已经完成布局
        postDelayed(new Runnable() {
            @Override
            public void run() {
                updateWindowSizeToContent();
            }
        }, 50); // 延迟50ms确保布局完成
    }
    
    /**
     * 添加自定义通知项
     */
    private void addCustomNotificationItem(MiStatusBarNotification miSbn) {
        try {
            // 创建自定义通知视图项
            MiRightCustomNotificationItem customView = new MiRightCustomNotificationItem(mContext);
            customView.setUp(miSbn, this);
            customView.setYThreshold(mTopMargin);
            
            // 添加到容器中
            this.removeAllViews();
            
            RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(
                    LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT); 
            
            rlp.topMargin = mTopMargin;
            rlp.addRule(RelativeLayout.CENTER_HORIZONTAL);
            this.addView(customView, rlp);
            
            mCurrentItem = customView;
            mCurrentSbn = miSbn;
            
            customView.setOnClickListener(v -> customView.executePendingIntent());
            
            removeCallbacks(mHideRunnable);
            // 停留几秒后隐藏
            postDelayed(mHideRunnable, HIDE_NOTIFICATION_DELAY);

            Log.d(TAG, "Added custom notification item using MiRightCustomNotificationItem11");
            Log.d("yyh123", "addCustomNotificationItem: mCurrentItem set to " + mCurrentItem);

            // 在下一个UI循环中更新窗口大小，确保view已经完成布局
            postDelayed(new Runnable() {
                @Override
                public void run() {
                    updateWindowSizeToContent();
                }
            }, 50); // 延迟50ms确保布局完成
        } catch (Exception e) {
            Log.e(TAG, "Error adding custom notification item", e);
            // 如果自定义视图添加失败，回退到标准视图
            addStandardNotificationItem(miSbn);
        }
    }

    private final Runnable mHideRunnable = () -> {
        Log.d(TAG, "hide top notification");
        dismiss();
    };

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        Log.d("yyh123", "MiRightNotificationContainer dispatchTouchEvent: action=" + event.getAction()
            + ", x=" + event.getX() + ", y=" + event.getY());

        boolean inNotificationArea = isTouchInNotificationArea(event);
        Log.d("yyh123", "dispatchTouchEvent inNotificationArea=" + inNotificationArea);

        if (inNotificationArea) {
            // 触摸在通知内容区域内，正常分发事件
            Log.d("yyh123", "dispatchTouchEvent: dispatching touch in notification area");
            return super.dispatchTouchEvent(event);
        } else {
            // 触摸在空白区域，完全不处理事件，让系统将事件传递给下层窗口
            Log.d("yyh123", "dispatchTouchEvent: touch in blank area, returning false for complete passthrough");
            return false;
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        Log.d("yyh123", "MiRightNotificationContainer onTouchEvent: action=" + event.getAction()
            + ", x=" + event.getX() + ", y=" + event.getY());

        boolean inNotificationArea = isTouchInNotificationArea(event);
        Log.d("yyh123", "onTouchEvent inNotificationArea=" + inNotificationArea);

        if (inNotificationArea) {
            // 触摸在通知内容区域内，正常处理
            Log.d("yyh123", "onTouchEvent: handling touch in notification area");
            return super.onTouchEvent(event);
        } else {
            // 触摸在空白区域，不处理触摸事件，让事件穿透到下层
            Log.d("yyh123", "onTouchEvent: touch in blank area, returning false for passthrough");
            return false;
        }
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        Log.d("yyh123", "MiRightNotificationContainer onInterceptTouchEvent: action=" + event.getAction()
            + ", x=" + event.getX() + ", y=" + event.getY());

        boolean inNotificationArea = isTouchInNotificationArea(event);
        Log.d("yyh123", "onInterceptTouchEvent inNotificationArea=" + inNotificationArea);

        if (inNotificationArea) {
            // 触摸在通知内容区域内，拦截事件进行处理
            Log.d("yyh123", "onInterceptTouchEvent: intercepting touch in notification area");
            return super.onInterceptTouchEvent(event);
        } else {
            // 触摸在空白区域，不拦截事件，让事件穿透到下层
            Log.d("yyh123", "onInterceptTouchEvent: touch in blank area, returning false for passthrough");
            return false;
        }
    }

    /**
     * 检查触摸点是否在通知内容区域内
     */
    private boolean isTouchInNotificationArea(MotionEvent event) {
        if (mCurrentItem == null) {
            Log.d("yyh123", "isTouchInNotificationArea: mCurrentItem is null");
            return false;
        }

        if (mCurrentItem.getVisibility() != View.VISIBLE) {
            Log.d("yyh123", "isTouchInNotificationArea: mCurrentItem is not visible, visibility=" + mCurrentItem.getVisibility());
            return false;
        }

        // 获取当前通知项的边界
        Rect itemBounds = new Rect();
        mCurrentItem.getHitRect(itemBounds);

        // 检查触摸点是否在通知项的范围内
        int x = (int) event.getX();
        int y = (int) event.getY();

        Log.d("yyh123", "isTouchInNotificationArea: touch(" + x + "," + y + "), bounds=" + itemBounds);

        boolean contains = itemBounds.contains(x, y);
        Log.d("yyh123", "isTouchInNotificationArea: contains=" + contains);

        return contains;
    }

    /**
     * 根据通知内容调整窗口大小，只覆盖实际需要的区域
     */
    protected void updateWindowSizeToContent() {
        if (mWindowManager == null) {
            Log.d("yyh123", "updateWindowSizeToContent: mWindowManager is null");
            return;
        }

        if (mCurrentItem == null || mCurrentItem.getVisibility() != View.VISIBLE) {
            Log.d("yyh123", "updateWindowSizeToContent: no current item or item not visible");
            return;
        }

        try {
            // 获取通知项在容器中的实际位置和大小
            Rect itemBounds = new Rect();
            mCurrentItem.getHitRect(itemBounds);

            Log.d("yyh123", "updateWindowSizeToContent: item bounds=" + itemBounds);

            // 计算窗口需要的实际大小和位置
            int windowWidth = itemBounds.width() + 100; // 添加一些边距
            int windowHeight = itemBounds.height() + mTopMargin + 50; // 添加一些边距

            // 计算窗口的X偏移，让通知内容居中显示
            int screenWidth = mContext.getResources().getDisplayMetrics().widthPixels;
            int windowX = (screenWidth - windowWidth) / 2;

            Log.d("yyh123", "updateWindowSizeToContent: calculated window size=" + windowWidth + "x" + windowHeight + ", x=" + windowX);

            // 获取当前窗口参数
            WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
            if (layoutParams != null) {
                // 调整窗口大小和位置
                layoutParams.width = windowWidth;
                layoutParams.height = windowHeight;
                layoutParams.x = windowX;
                layoutParams.y = 0;

                // 移除FLAG_NOT_TOUCH_MODAL，让窗口正常处理触摸事件，但只在窗口范围内
                layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
                layoutParams.gravity = Gravity.TOP | Gravity.LEFT; // 使用绝对定位

                mWindowManager.updateViewLayout(this, layoutParams);
                Log.d("yyh123", "updateWindowSizeToContent: updated window to size=" + layoutParams.width + "x" + layoutParams.height + ", position=(" + layoutParams.x + "," + layoutParams.y + ")");
            }
        } catch (Exception e) {
            Log.e("yyh123", "updateWindowSizeToContent: failed to update window size", e);
        }
    }

    /**
     * 重置窗口为全屏宽度的穿透模式
     */
    private void resetToPassthroughMode() {
        if (mWindowManager == null) {
            return;
        }

        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
        if (layoutParams != null) {
            // 恢复全屏宽度和穿透模式
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.x = 0;
            layoutParams.y = 0;
            layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                    | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.TOP; // 恢复居中对齐
            try {
                mWindowManager.updateViewLayout(this, layoutParams);
                Log.d("yyh123", "resetToPassthroughMode: reset to full width passthrough mode");
            } catch (Exception e) {
                Log.e("yyh123", "resetToPassthroughMode: failed to reset layout", e);
            }
        }
    }
}
