package com.android.systemui.qs.mi.notification;

import android.content.Context;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.util.Log;
import android.view.*;
import android.widget.RelativeLayout;

import javax.swing.text.View;

import com.android.systemui.R;
import com.android.systemui.statusbar.phone.StatusBar;

// 每次只有一个View展示
// Top 消息通知
public class MiRightNotificationContainer extends RelativeLayout implements MiNotificationInternalListener {

    private static final String TAG = "MiRightNotificationContainer";
    private static final int HIDE_NOTIFICATION_DELAY = 3500;

    protected final Context mContext;
    protected MiStatusBarNotification mCurrentSbn;
    protected View mCurrentItem;

    protected WindowManager mWindowManager;
    private StatusBar mStatusBar;
    protected int mTopMargin;

    public MiRightNotificationContainer(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public void setStatusBar(StatusBar statusBar) {
        mStatusBar = statusBar;
    }

    // 如果是正在显示横幅消息，就不显示状态栏上的图标
    public boolean isShowing() {
        return getVisibility() == View.VISIBLE;
    }

    public void hide() {
        setVisibility(View.GONE);
    }

    public void dismiss() {
        hide();
    }

    public void show() {
        setVisibility(View.VISIBLE);
    }

    @Override
    public void onNotificationPosted(MiStatusBarNotification miSbn) {
        if (miSbn == null) return;
        if (miSbn.isPersistentNotification()) {
            return;
        }
        Log.d(TAG, "onNotificationPosted, " + miSbn.getUniqueId());
        if (miSbn.isForbidPopupNotification()) {
            Log.d(TAG, "forbid popup notification.");
            return;
        }
        if (MiNotificationCenterData.getInstance().isCenterActivityResumed()) {
            return;
        }

        addNotificationItem(miSbn);
        show();
        postDelayed(new Runnable() {
            @Override
            public void run() {
                updateWindowSizeToContent(false);
            }
        }, 1500);
    }

    @Override
    public void onNotificationRemoved(MiStatusBarNotification miSbn) {
        if (miSbn == null || mCurrentSbn == null) return;
        if (miSbn.isPersistentNotification()) {
            return;
        }
        Log.d(TAG, "onNotificationRemoved, " + miSbn.getUniqueId());
        if (mCurrentSbn.getUniqueId().equals(miSbn.getUniqueId()) && mCurrentItem.isAttachedToWindow()) {
            this.removeView(mCurrentItem);
        }
        removeCallbacks(mHideRunnable);

        dismiss();
    }

    private void init() {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.TOP;
        layoutParams.format = PixelFormat.TRANSLUCENT;
        layoutParams.windowAnimations = R.style.Animation_Notification;
        mWindowManager = mContext.getSystemService(WindowManager.class);
        mWindowManager.addView(this, layoutParams);
        hide();

        mTopMargin = getResources().getDimensionPixelSize(R.dimen.mi_notification_view_top_margin);
    }

    public void deInit() {
        mStatusBar = null;
        if (mWindowManager != null) {
            mWindowManager.removeView(this);
            mWindowManager = null;
        }
        MiNotificationManager.getManager(mContext).unregisterInternalMiListener(this);
    }

    public void addNotificationItem(MiStatusBarNotification miSbn) {
        Log.d(TAG, "show notification view");
        
        // 检查是否有自定义RemoteView
        if (miSbn.hasCustomContentView()) {
            // 使用自定义RemoteView
            addCustomNotificationItem(miSbn);
        } else {
            // 使用标准通知视图
            addStandardNotificationItem(miSbn);
        }
        updateWindowSizeToContent(true);
    }
    
    /**
     * 添加标准通知项
     */
    private void addStandardNotificationItem(MiStatusBarNotification miSbn) {
        MiRightNotificationItem view = new MiRightNotificationItem(mContext);
        view.setUp(miSbn, this);
        view.setYThreshold(mTopMargin);
        this.removeAllViews();

        RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(
                LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        rlp.topMargin = mTopMargin;
        rlp.addRule(RelativeLayout.CENTER_HORIZONTAL);
        this.addView(view, rlp);
        mCurrentItem = view;
        mCurrentSbn = miSbn;

        view.setOnClickListener(v -> view.executePendingIntent());

        removeCallbacks(mHideRunnable);
        // 停留几秒后隐藏
        postDelayed(mHideRunnable, HIDE_NOTIFICATION_DELAY);
        
        Log.d(TAG, "Added standard notification item");
    }
    
    /**
     * 添加自定义通知项
     */
    private void addCustomNotificationItem(MiStatusBarNotification miSbn) {
        try {
            // 创建自定义通知视图项
            MiRightCustomNotificationItem customView = new MiRightCustomNotificationItem(mContext);
            customView.setUp(miSbn, this);
            customView.setYThreshold(mTopMargin);
            
            // 添加到容器中
            this.removeAllViews();
            
            RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(
                    LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT); 
            
            rlp.topMargin = mTopMargin;
            rlp.addRule(RelativeLayout.CENTER_HORIZONTAL);
            this.addView(customView, rlp);
            
            mCurrentItem = customView;
            mCurrentSbn = miSbn;
            
            customView.setOnClickListener(v -> customView.executePendingIntent());
            
            removeCallbacks(mHideRunnable);
            // 停留几秒后隐藏
            postDelayed(mHideRunnable, HIDE_NOTIFICATION_DELAY);
            
            Log.d(TAG, "Added custom notification item using MiRightCustomNotificationItem11");
        } catch (Exception e) {
            Log.e(TAG, "Error adding custom notification item", e);
            // 如果自定义视图添加失败，回退到标准视图
            addStandardNotificationItem(miSbn);
        }
    }

    private final Runnable mHideRunnable = () -> {
        Log.d(TAG, "hide top notification");
        dismiss();
    };

    /**
     * 根据通知内容调整窗口大小，只覆盖实际需要的区域
     * isReset true:重置宽度为全屏 false:根据通知内容调整宽度
     */
    protected void updateWindowSizeToContent(boolean isReset) {
        if (mWindowManager == null) {
            Log.d(TAG, "updateWindowSizeToContent: mWindowManager is null");
            return;
        } else if (isReset) {
            // 获取当前窗口参数
            WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
            if (layoutParams != null) {
                // 调整窗口大小
                layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
                layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
                layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
                layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                        | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
                layoutParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.TOP;
                layoutParams.format = PixelFormat.TRANSLUCENT;
                layoutParams.windowAnimations = R.style.Animation_Notification;

                mWindowManager.updateViewLayout(this, layoutParams);
                Log.d(TAG, "updateWindowSizeToContent: reset window width");
            }
            return;
        }


        if (mCurrentItem == null || mCurrentItem.getVisibility() != View.VISIBLE) {
            Log.d(TAG, "updateWindowSizeToContent: no current item or item not visible");
            return;
        }

        try {
            // 获取通知项在容器中的实际位置和大小
            Rect itemBounds = new Rect();
            mCurrentItem.getHitRect(itemBounds);

            Log.d(TAG, "updateWindowSizeToContent: item bounds=" + itemBounds);

            // 计算窗口需要的实际大小
            int windowWidth = itemBounds.width();
            int windowHeight = itemBounds.height() + mTopMargin;

            Log.d(TAG, "updateWindowSizeToContent: calculated window size=" + windowWidth + "x" + windowHeight);

            // 获取当前窗口参数
            WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
            if (layoutParams != null) {
                // 调整窗口大小
                layoutParams.width = windowWidth;
                layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
                layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
                layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                        | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
                layoutParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.TOP;
                layoutParams.format = PixelFormat.TRANSLUCENT;
                layoutParams.windowAnimations = R.style.Animation_Notification;

                mWindowManager.updateViewLayout(this, layoutParams);
                Log.d(TAG, "updateWindowSizeToContent: updated window to size=" + layoutParams.width + "x" + layoutParams.height);
            }
        } catch (Exception e) {
            Log.e(TAG, "updateWindowSizeToContent: failed to update window size", e);
        }
    }
}
