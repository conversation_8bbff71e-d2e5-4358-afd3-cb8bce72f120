package com.android.systemui.qs.mi.notification;

import android.content.Context;
import android.os.PowerManager;
import android.os.SystemClock;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.RemoteViews;

import com.android.systemui.R;
import com.android.systemui.qs.mi.utils.MiSoundUtils;
import com.android.systemui.qs.mi.notification.MiRightCustomNotificationItem;

// 每次只有一个View展示
// Top 消息通知
// 常驻通知，用于通知不会主动消失的需求，例如监控通知
public class MiRightPersistentNotificationContainer extends MiRightNotificationContainer {

    private static final String TAG = "MiRightPersistentNotificationContainer";

    public MiRightPersistentNotificationContainer(Context context) {
        super(context);
    }

    @Override
    public void onNotificationPosted(MiStatusBarNotification miSbn) {
        if (miSbn == null) return;
        if (miSbn.isPersistentNotification()) {
            Log.d(TAG, "Persistent notification posted: " + 
                  (miSbn.getSbn() != null ? miSbn.getSbn().getPackageName() : "unknown") + 
                  ", hasCustomView: " + miSbn.hasCustomContentView());
            addNotificationItem(miSbn);
            if (!MiNotificationCenterData.getInstance().isCenterActivityResumed()) {
                show();
            }
            MiSoundUtils.playSound(mContext, R.raw.notice);
            PowerManager powerManager = (PowerManager) getContext().getSystemService(Context.POWER_SERVICE);
            if (!powerManager.isScreenOn()) {
                powerManager.wakeUp(SystemClock.uptimeMillis(), "camera monitor wake up.");
            }
        }
    }

    @Override
    public void onNotificationRemoved(MiStatusBarNotification miSbn) {
        if (miSbn == null || mCurrentSbn == null) return;
        if (mCurrentSbn.getUniqueId().equals(miSbn.getUniqueId()) && mCurrentItem.isAttachedToWindow()) {
            this.removeView(mCurrentItem);
            dismiss();
        }
    }

    @Override
    public void addNotificationItem(MiStatusBarNotification miSbn) {
        Log.d(TAG, "Show persistent notification view, hasCustomView: " + miSbn.hasCustomContentView());
        
        // 检查是否有自定义RemoteView
        if (miSbn.hasCustomContentView()) {
            // 使用自定义RemoteView
            addCustomNotificationItem(miSbn);
        } else {
            // 使用标准通知视图
            addStandardNotificationItem(miSbn);
        }

        post(new Runnable() {
            @Override
            public void run() {
                updateWindowSizeToContent();
            }
        });
    }
    
    /**
     * 添加标准通知项
     */
    private void addStandardNotificationItem(MiStatusBarNotification miSbn) {
        MiRightNotificationItem view = new MiRightNotificationItem(mContext);
        view.setUp(miSbn, this);
        view.setYThreshold(mTopMargin);

        Button btnExtraOperate = view.findViewById(R.id.btn_extra_operate);
        btnExtraOperate.setVisibility(VISIBLE);
        btnExtraOperate.setOnClickListener(v -> view.executePendingIntent());
        btnExtraOperate.setText(miSbn.getExtraButtonText());

        this.removeAllViews();

        RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(
                    LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        rlp.topMargin = mTopMargin;
        rlp.addRule(RelativeLayout.CENTER_HORIZONTAL);
        this.addView(view, rlp);
        mCurrentItem = view;
        mCurrentSbn = miSbn;

        Log.d(TAG, "Added standard notification item");
        Log.d("yyh123", "MiRightPersistentNotificationContainer addStandardNotificationItem: mCurrentItem set to " + mCurrentItem);

    }
    
    /**
     * 添加自定义通知项
     */
    private void addCustomNotificationItem(MiStatusBarNotification miSbn) {
        try {
            // 创建自定义通知视图项
            MiRightCustomNotificationItem customView = new MiRightCustomNotificationItem(mContext);
            customView.setUp(miSbn, this);
            customView.setYThreshold(mTopMargin);

            // 添加到容器中
            this.removeAllViews();

            RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(
                    LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
            rlp.topMargin = mTopMargin;
            rlp.addRule(RelativeLayout.CENTER_HORIZONTAL);
            this.addView(customView, rlp);

            mCurrentItem = customView;
            mCurrentSbn = miSbn;

            Log.d(TAG, "Added custom notification item using MiRightCustomNotificationItem22");
            Log.d("yyh123", "MiRightPersistentNotificationContainer addCustomNotificationItem: mCurrentItem set to " + mCurrentItem);

        } catch (Exception e) {
            Log.e(TAG, "Error adding custom notification item", e);
            // 如果自定义视图添加失败，回退到标准视图
            addStandardNotificationItem(miSbn);
        }
    }

    public void updateVisible() {
        if (mCurrentItem != null) {
            boolean hasSlideDismiss = false;
            if (mCurrentItem instanceof MiRightNotificationItem) {
                hasSlideDismiss = ((MiRightNotificationItem) mCurrentItem).hasSlideDismiss();
            } else if (mCurrentItem instanceof MiRightCustomNotificationItem) {
                hasSlideDismiss = ((MiRightCustomNotificationItem) mCurrentItem).hasSlideDismiss();
            }
            setVisibility(hasSlideDismiss ? GONE : VISIBLE);
        }
    }
}
