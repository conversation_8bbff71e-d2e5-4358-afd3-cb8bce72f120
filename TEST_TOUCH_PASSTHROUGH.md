# 触摸穿透测试指南

## 当前问题分析

根据您提供的日志：
```
2025-07-29 13:55:21.046   609-609   yyh123   com.android.systemui   D  MiRightNotificationContainer dispatchTouchEvent: action=0, x=1214.0515, y=58.876404
2025-07-29 13:55:21.046   609-609   yyh123   com.android.systemui   D  isTouchInNotificationArea: touch(1214,58), bounds=Rect(355, 24 - 925, 136)
2025-07-29 13:55:21.046   609-609   yyh123   com.android.systemui   D  isTouchInNotificationArea: contains=false
2025-07-29 13:55:21.046   609-609   yyh123   com.android.systemui   D  dispatchTouchEvent inNotificationArea=false
2025-07-29 13:55:21.047   609-609   yyh123   com.android.systemui   D  dispatchTouchEvent: touch in blank area, returning false for complete passthrough
```

**问题确认**：
- 触摸点在 (1214, 58)
- 通知内容边界是 Rect(355, 24 - 925, 136)
- 触摸点1214确实在空白区域（超出了925px的右边界）
- 我们正确返回了false，但下层组件仍然没有响应

**根本原因**：即使我们返回false，Android系统也不会将事件传递给下层窗口，因为我们的窗口仍然覆盖了整个屏幕宽度。

## 解决方案：动态窗口大小调整

我已经实现了动态窗口大小调整的解决方案。现在需要测试新的实现。

## 测试步骤

### 1. 编译并安装修改后的代码

### 2. 触发通知显示
显示一个通知，观察以下日志：

```bash
adb logcat | grep yyh123
```

**期望看到的日志**：
```
D  addStandardNotificationItem: mCurrentItem set to [View对象]
D  updateWindowSizeToContent: item bounds=Rect(355, 24 - 925, 136)
D  updateWindowSizeToContent: calculated window size=670x162, x=165
D  updateWindowSizeToContent: updated window to size=670x162, position=(165,0)
```

### 3. 测试触摸事件

#### 3.1 点击通知内容区域
- **操作**：点击通知内容
- **期望结果**：通知正常响应点击
- **期望日志**：
```
D  dispatchTouchEvent: action=0, x=[在通知范围内的坐标]
D  isTouchInNotificationArea: contains=true
D  dispatchTouchEvent: dispatching touch in notification area
```

#### 3.2 点击左侧空白区域
- **操作**：点击通知左侧的空白区域
- **期望结果**：下层组件响应点击
- **期望日志**：应该没有我们的日志输出，因为窗口不再覆盖该区域

#### 3.3 点击右侧空白区域
- **操作**：点击通知右侧的空白区域
- **期望结果**：下层组件响应点击
- **期望日志**：应该没有我们的日志输出，因为窗口不再覆盖该区域

### 4. 测试通知隐藏

#### 4.1 通知自动隐藏
- **操作**：等待通知自动隐藏（3.5秒后）
- **期望日志**：
```
D  hide: notification hidden, resetting to passthrough mode
D  resetToPassthroughMode: reset to full width passthrough mode
```

#### 4.2 手动隐藏通知
- **操作**：手动触发通知隐藏
- **期望结果**：窗口重置为全屏穿透模式

## 故障排除

### 如果窗口大小没有正确调整

检查日志中是否有：
```
E  updateWindowSizeToContent: failed to update window size
```

可能的原因：
1. mCurrentItem为null
2. WindowManager操作失败
3. 测量过程出错

### 如果触摸事件仍然被拦截

1. 确认窗口大小确实已经调整
2. 检查WindowManager.LayoutParams的flags设置
3. 验证gravity设置是否正确

### 如果通知显示位置不正确

检查以下参数：
- layoutParams.x 和 layoutParams.y
- layoutParams.gravity
- 屏幕宽度计算

## 预期效果

成功实现后：
1. **通知显示时**：窗口只覆盖通知内容区域，左右空白区域完全暴露给下层组件
2. **触摸通知内容**：正常响应
3. **触摸空白区域**：下层组件正常响应，没有任何拦截
4. **通知隐藏时**：窗口重置为全屏穿透模式

## 调试命令

```bash
# 查看窗口信息
adb shell dumpsys window windows | grep -A 10 -B 10 MiRightNotificationContainer

# 查看触摸事件
adb shell getevent | grep ABS_MT

# 实时查看我们的日志
adb logcat -s yyh123:D
```

这个解决方案应该能够彻底解决触摸穿透问题，因为它从根本上消除了空白区域的存在。
